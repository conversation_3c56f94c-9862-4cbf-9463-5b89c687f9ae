import { ComputedRef } from 'vue';

export type LinkedDetail = {
  title: string;
  value: ComputedRef<string> | string;
  type: 'link';
  href: string;
};

export type TextDetail = {
  title: string;
  value: ComputedRef<string> | string;
  type: 'text';
};

export type ListDetail = {
  title: string;
  type: 'list';
  items: Array<LinkedDetail>;
};

export type BooleanDetail = {
  title: string;
  type: 'boolean';
  value: ComputedRef<boolean | null> | boolean | null;
};

export type OptionDetail = {
  title: string;
  type: 'option';
  value: ComputedRef<string | null> | string | null;
};

export type ViewDetail =
  | TextDetail
  | LinkedDetail
  | ListDetail
  | BooleanDetail
  | OptionDetail;
