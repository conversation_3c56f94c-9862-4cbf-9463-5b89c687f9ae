// Branded types
export { type Branded } from './Branded';

// Common utility types
export {
  type Maybe,
  type Nullable,
  type ArrayElement,
  type DeepNullable,
  type NonNullableFields,
  type Options,
  type WithRequired,
  type WithPartialRequired,
  type AnyFunction
} from './Common';

// Country types
export { type Country } from './Country';

// HTML and Vue types
export { type VueClassProp } from './Html';
export { type VueClassProp as VueClassPropHtml } from './html/types';

// Tippy configuration types
export { type ITippyConfig } from './ITippy';

// Mailable interface
export { type Mailable } from './Mailable';

// Modal types and configuration
export {
  type ModalConfig,
  type ModalAPI,
  defaultConfig as defaultModalConfig
} from './Modal';

// Pagination types
export { type Paginable } from './Pagination';

// Person interface
export { type Person } from './Person';

// Relationship types and enums
export {
  type Relation,
  RelationshipType
} from './Relationship';

// Sidebar types
export { type SidebarItem } from './Sidebar';

// View detail types
export {
  type LinkedDetail,
  type TextDetail,
  type ListDetail,
  type BooleanDetail,
  type OptionDetail,
  type ViewDetail
} from './ViewDetail';

// API types - re-export from api index
export * from './api';

// Store reference data types
export {
  type RefDataDTO,
  type OptionsRefDataDTO,
  type SelectOptionRefDataDTO,
  type EnumRefDataDTO,
  type Provider,
  // TODO: Module "./stores" has already exported a member named 'Goal'.
  // type Goal,
  type Cashflow,
  type ProductType,
  type ProductTypeGroup,
  type AdviceType,
  type AdviceGroup,
  type Country as StoreCountry,
  type ClientSource,
  type NoEmailReason,
  type AdvisorRole,
  type DocumentTemplateTypeDTO,
  type RefDataBlobDTO
} from './store/Refdata';