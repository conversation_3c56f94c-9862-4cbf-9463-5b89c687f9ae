import { ClientId } from '@modules/clients';
import { IDateTime } from '@/utils/dateTime';

export interface Relation {
  relatedClientId: ClientId;
  relationshipType: RelationshipType;
  firstName: string;
  lastName: string | null;
  dateOfBirth: IDateTime | null;
  relationshipId: number;
  nationalityId: number | null;
  genderId: number;
}

export enum RelationshipType {
  NotSet = 0,
  Spouse = 1,
  Child = 2,
  Parent = 3,
  Friend = 4,
  BusinessPartner = 5,
  Director = 6,
  Shareholder = 7,
  Beneficiary = 8,
  Trustee = 9,
  BusinessDirector = 10,
  BusinessShareholder = 11,
  TrustBeneficiary = 12,
  TrustTrustee = 13,
  Sibling = 14,
  Grandparent = 15,
  <PERSON><PERSON> = 16,
}
