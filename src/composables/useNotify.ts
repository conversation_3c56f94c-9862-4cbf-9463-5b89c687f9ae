import { VNode, h, reactive, ref } from 'vue';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import Alert from '../components/Alert.vue';

type AlertInstance = InstanceType<typeof Alert>;

function renderComponent({ props }: { props: AlertInstance['$props'] }) {
  return h(Alert, props);
}

export const useNotify = () => {
  const notifications = reactive<VNode[]>([]);
  const id = ref(0);

  const success = async (message: string) => {
    const vnode = renderComponent({
      props: {
        type: 'success',
        message,
        id: id.value++,
        dismissible: true,
        onDismiss: handleDismiss,
      },
    });

    notifications.push(vnode);
  };

  const warning = async (message: string) => {
    const vnode = renderComponent({
      props: {
        type: 'warning',
        message,
        id: id.value++,
        dismissible: true,
        onDismiss: handleDismiss,
      },
    });

    notifications.push(vnode);
  };

  const error = async (message: string) => {
    const vnode = renderComponent({
      props: {
        type: 'error',
        message,
        id: id.value++,
        dismissible: true,
        onDismiss: handleDismiss,
      },
    });

    notifications.push(vnode);
  };

  const handleDismiss = (id: number) => {
    const removeIndex = notifications.findIndex((n) => {
      return (n.props as AlertInstance['$props']).id === id;
    });

    notifications.splice(removeIndex, 1);
  };

  return {
    success,
    warning,
    error,
    notifications,
  };
};
