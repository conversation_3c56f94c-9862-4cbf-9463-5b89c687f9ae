import ModalConfirmation from '@/components/ModalConfirmation.vue';
import { createConfirmDialog } from 'vuejs-confirm-dialog';
import { DefineComponent, ref } from 'vue';

export const useConfirmation = async (message: string, title?: string) => {
  const answer = ref<boolean>(false);

  const { onConfirm, onCancel, reveal } = createConfirmDialog(
    ModalConfirmation as DefineComponent<
      any,
      any,
      any,
      any,
      any,
      any,
      any,
      any
    >,
  );

  onConfirm(() => {
    answer.value = true;
  });

  onCancel(() => {
    answer.value = false;
  });

  await reveal({
    message: message,
    title: title,
  });

  return {
    onAccept: (cb: CallableFunction) => {
      if (answer.value) cb();
    },

    onCancel: (cb: CallableFunction) => {
      if (!answer.value) cb();
    },

    isAccepted: () => {
      return Boolean(answer.value);
    },
  };
};
