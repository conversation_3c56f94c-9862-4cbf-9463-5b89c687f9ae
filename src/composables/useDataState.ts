/** DEPRECATED **/
import { Ref, ref, unref } from 'vue';

export type State = 'LOADING' | 'READY' | 'FAILED';

export const isReadyState = (state: State | Ref<State>) =>
  unref(state) === 'READY';
export const isLoadingState = (state: State | Ref<State>) =>
  unref(state) === 'LOADING';
export const isFailedState = (state: State | Ref<State>) =>
  unref(state) === 'FAILED';

export function useDataState(initialValue: State = 'LOADING') {
  const dataState = ref<State>(initialValue);

  const setLoadingState = () => (dataState.value = 'LOADING');
  const setReadyState = () => (dataState.value = 'READY');
  const setFailedState = () => (dataState.value = 'FAILED');

  return {
    dataState,
    setLoadingState,
    setReadyState,
    setFailedState,
  };
}
