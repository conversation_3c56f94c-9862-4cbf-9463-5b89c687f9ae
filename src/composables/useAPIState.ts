import { computed } from 'vue';
import { useStorage } from '@vueuse/core';

interface APIState {
  isLoading: boolean;
  error?: string;
}

export const useAPIState = () => {
  const state = useStorage<APIState>('apiState', {
    isLoading: false,
    error: undefined,
  });
  return {
    APIState: state,
    isLoading: computed(() => state.value.isLoading),
    setState($s: boolean) {
      state.value.isLoading = $s;
    },
    setError($e?: Error) {
      state.value.error = [$e?.name, $e?.message].filter((_) => _).join(': ');
    },
  };
};
