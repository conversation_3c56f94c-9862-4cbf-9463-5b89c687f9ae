// API State composable
export { useAPIState } from './useAPIState';

// Age utilities and composable
export {
  birthdateInjectionKey,
  CURRENT_DATETIME,
  useAge,
  type AgeRangeType
} from './useAge';

// Click outside composable
export { useClickOutside } from './useClickOutside';

// Client status composable (default export)
export { default as useClientStatus } from './useClientStatus';

// Confirmation composable
export { useConfirmation } from './useConfirmation';

// Data state utilities and composable
export {
  useDataState,
  isReadyState,
  isLoadingState,
  isFailedState,
  type State
} from './useDataState';

// JWT utilities
export { isTokenExpired } from './useJWT';

// Linked client list composable
export { useLinkedClientList } from './useLinkedClientList';

// TODO: Duplicate of utils/list/use-list-crud.ts
// List CRUD composable and types
// export {
//   useListCrud,
//   default as useListCrudDefault,
//   type ListElement
// } from './useListCrud';

// Notification composable
export { useNotify } from './useNotify';

// Postmark composable
export { usePostmark } from './usePostmark';

// Products composable
export { useProducts } from './useProducts';

// Realtime composables and classes
export {
  default as useRealtime,
  RealtimeMessage,
  RealtimeChannel
} from './useRealtime';

// Route utilities and types
export {
  default as useRouteUtils,
  type Route
} from './useRoute';

// Toast composable and utilities
export {
  useToast,
  toastPluginOptions,
  type ToastOptions
} from './useToast';

// Valuation process composable
export { useValuationProcess } from './useValuation';
