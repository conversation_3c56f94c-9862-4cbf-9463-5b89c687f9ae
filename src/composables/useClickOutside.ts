import { Ref, onBeforeUnmount, onMounted, ref } from 'vue';

type Handler = (event: MouseEvent) => void;

export const useClickOutside = (
  htmlElementRef: Ref | null,
  handler: Handler,
) => {
  const listener = ref<typeof handler>();

  onMounted(() => {
    listener.value = (event: MouseEvent) => {
      if (
        !htmlElementRef?.value ||
        htmlElementRef?.value.contains(event.target as Node)
      )
        return;

      handler(event);
    };

    window.addEventListener('mousedown', listener.value);
  });

  onBeforeUnmount(() => {
    if (listener.value) {
      window.removeEventListener('mousedown', listener.value);
    }
  });
};
