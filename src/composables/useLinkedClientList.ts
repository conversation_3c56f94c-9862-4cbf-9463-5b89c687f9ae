import { compact } from 'lodash';
import { MaybeRef } from '@vueuse/core';
import { Ref, computed, toRef } from 'vue';

import { formatName } from '@/utils/user';
import { Client } from '@modules/clients';
import { ClientLink } from '@modules/clients/models';
import { SelectOption } from '@/components/form/fields/field-model';

export function useLinkedClientList(
  client: MaybeRef<Client>,
  includeSelf = true,
) {
  client = toRef(client);

  const linkedClients: Ref<SelectOption[]> = computed(() => {
    const clients = [] as Array<SelectOption | undefined>;

    if (client.value) {
      clients.push(
        includeSelf
          ? {
              label: formatName(client.value),
              value: client.value.id,
              disabled: true,
            }
          : undefined,
        ...client.value.linkedClients.map((link: ClientLink) => ({
          label: formatName(link),
          value: link.linkedClientId,
          disabled: false,
        })),
      );
    }

    return compact(clients);
  });

  return { linkedClients };
}
