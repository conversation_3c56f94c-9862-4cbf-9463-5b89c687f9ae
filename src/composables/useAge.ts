// migrate to `date-fns`
import { InjectionKey, Ref } from 'vue';
import { DateTime } from 'luxon';
import { partial } from 'lodash';

export const birthdateInjectionKey = Symbol() as InjectionKey<
  Ref<string | undefined | null>
>;
export const CURRENT_DATETIME = DateTime.utc();

export type AgeRangeType = [number, number | never];
export const useAge = (birthDateRef: Ref<string>) => {
  function calculateAge(
    ref: Ref<string>,
    dateString: string,
    strict = false,
  ): number {
    const birthDate = new Date(ref.value);
    const date = new Date(dateString);

    if (strict) {
      const ageDiffInMs = date.getTime() - birthDate.getTime();
      const ageDate = new Date(ageDiffInMs);

      return Math.abs(ageDate.getUTCFullYear() - 1970);
    }

    return date.getUTCFullYear() - birthDate.getUTCFullYear();
  }

  function calculateDate(ref: Ref<string>, age: number): string {
    const birthDate = new Date(ref.value);
    const currentYear = new Date().getFullYear();
    const endOfYearDate = new Date(`${currentYear}-12-31T23:59:59.999`);
    const birthYear = birthDate.getFullYear();
    const targetYear = currentYear + age - (currentYear - birthYear);
    endOfYearDate.setFullYear(targetYear);

    return endOfYearDate.toISOString().slice(0, 10);
  }

  return {
    date2age: partial(calculateAge, birthDateRef),
    age2date: partial(calculateDate, birthDateRef),
  };
};
