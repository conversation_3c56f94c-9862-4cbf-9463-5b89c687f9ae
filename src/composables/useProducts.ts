import { fetchAccountLikeProducts } from '@modules/refdata/api/fetchProductTypes';
import { ComputedRef, computed } from 'vue';
import { useProductStore } from '@modules/refdata/product/store';
import { storeToRefs } from 'pinia';
import { Product } from '@modules/refdata/types/Product';

interface UseProductsApi {
  getAccountLikeProducts: () => Promise<Product[]>;
  getOtherDebtProducts: ComputedRef<Product[]>;
  getCreditCardProducts: ComputedRef<Product[]>;
  getPersonalLoanProducts: ComputedRef<Product[]>;
  getMortgageProducts: ComputedRef<Product[]>;
  getOtherAssetProducts: ComputedRef<Product[]>;
  getCryptoCurrencyProducts: ComputedRef<Product[]>;
  getCompanySharesProducts: ComputedRef<Product[]>;
  getPropertyProducts: ComputedRef<Product[]>;
  getAccountProducts: ComputedRef<Product[]>;
  getTermPolicyProducts: ComputedRef<Product[]>;
  getIndemnityPolicyProducts: ComputedRef<Product[]>;
  getWholeOfLifePolicyProducts: ComputedRef<Product[]>;
  getIncomeProtectionPolicyProducts: ComputedRef<Product[]>;
  getDefinedBenefitPensionProducts: ComputedRef<Product[]>;
}

export function useProducts(): UseProductsApi {
  const storeRefs = storeToRefs(useProductStore());

  const getAccountLikeProducts = async () => {
    if (storeRefs.products.value.length) return storeRefs.products.value;
    return fetchAccountLikeProducts();
  };

  const getAccountProducts = computed(() => {
    return storeRefs.getProductsByType.value('account');
  });

  const getPropertyProducts = computed(() => {
    return storeRefs.getProductsByType.value('property');
  });

  const getCompanySharesProducts = computed(() => {
    return storeRefs.getProductsByType.value('company_shares');
  });

  const getCryptoCurrencyProducts = computed(() => {
    return storeRefs.getProductsByType.value('crypto_currency');
  });

  const getOtherAssetProducts = computed(() => {
    return storeRefs.getProductsByType.value('other_asset');
  });

  const getMortgageProducts = computed(() => {
    return storeRefs.getProductsByType.value('mortgage');
  });

  const getPersonalLoanProducts = computed(() => {
    return storeRefs.getProductsByType.value('personal_loan');
  });

  const getCreditCardProducts = computed(() => {
    return storeRefs.getProductsByType.value('credit_card');
  });

  const getOtherDebtProducts = computed(() => {
    return storeRefs.getProductsByType.value('other_debt');
  });

  const getTermPolicyProducts = computed(() => {
    return storeRefs.getProductsByType.value('term_policy');
  });

  const getIndemnityPolicyProducts = computed(() => {
    return storeRefs.getProductsByType.value('indemnity_policy');
  });

  const getWholeOfLifePolicyProducts = computed(() => {
    return storeRefs.getProductsByType.value('whole_of_life_policy');
  });

  const getIncomeProtectionPolicyProducts = computed(() => {
    return storeRefs.getProductsByType.value('income_protection_policy');
  });

  const getDefinedBenefitPensionProducts = computed(() => {
    return storeRefs.getProductsByType.value('defined_benefit_pension');
  });

  return {
    getAccountLikeProducts,
    getOtherDebtProducts,
    getCreditCardProducts,
    getPersonalLoanProducts,
    getMortgageProducts,
    getOtherAssetProducts,
    getCryptoCurrencyProducts,
    getCompanySharesProducts,
    getPropertyProducts,
    getAccountProducts,
    getTermPolicyProducts,
    getIndemnityPolicyProducts,
    getWholeOfLifePolicyProducts,
    getIncomeProtectionPolicyProducts,
    getDefinedBenefitPensionProducts,
  };
}
