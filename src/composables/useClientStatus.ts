import { ClientStatus, ClientStatuses } from '@modules/clients';
import { useRefData } from '@/stores';
import { find } from 'lodash';

const activeStatuses = (): ClientStatuses[] => {
  const { getClientStatuses: statuses } = useRefData();
  return statuses.filter((status: ClientStatuses) =>
    // Lead, Active Transactional, Dormant, Active Ongoing
    [1, 2, 3, 5].includes(status.id),
  );
};

const isActiveStatus = (id: ClientStatus): boolean =>
  !!find(activeStatuses(), { id });

const getById = (id: ClientStatus): ClientStatuses | null => {
  const { getClientStatuses: statuses } = useRefData();
  return statuses.find((status: ClientStatuses) => status.id === id) || null;
};

const getStatusNameById = (statusId: ClientStatus): string | undefined =>
  getById(statusId)?.name;

const useClientStatus = () => ({
  activeStatuses,
  isActiveStatus,
  getById,
  getStatusNameById,
});

export default useClientStatus;
