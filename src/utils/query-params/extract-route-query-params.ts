import { LocationQuery, LocationQueryValue } from 'vue-router';

export const extractRouteQueryParams = (
  routeQuery: LocationQuery,
  parametersToIgnore: string[],
): {
  [key: string]: string | number | LocationQueryValue | LocationQueryValue[];
} => {
  return Object.fromEntries(
    Object.entries(routeQuery)
      .filter((el) => typeof el[0] === 'string')
      .filter((el) => {
        return !parametersToIgnore.includes(el[0]);
      }),
  );
};
