import { computed } from 'vue';
import { useRoute } from 'vue-router';

export const useQueryParams = (groupName: string) => {
  const route = useRoute();
  const groupQuery = computed(() =>
    Object.fromEntries(
      Object.entries(route.query).filter((entry) =>
        entry[0].startsWith(groupName),
      ),
    ),
  );

  const unwrappedQuery = computed(() => {
    return Object.fromEntries(
      Object.entries(groupQuery.value).map((entry) => [
        unwrapKey(entry[0]),
        entry[1],
      ]),
    );
  });

  const createKey = (key: string): string =>
    `${key ? `${groupName}_${key}` : groupName}`;
  const unwrapKey = (key: string): string => key.replace(`${groupName}_`, '');

  const createQueryParams = (
    params: Array<[string, string]>,
    with_group: boolean = true,
  ): typeof route.query => {
    const urlSearchParams = new URLSearchParams({
      ...Object.fromEntries(
        params.map(([key, value]) => [
          with_group ? createKey(key) : key,
          value,
        ]),
      ),
    });

    return Object.fromEntries(urlSearchParams.entries());
  };

  const removeFromQueryParams = (keys: string[]): typeof route.query => {
    const urlSearchParams = new URLSearchParams(route.query as any);

    keys.map(createKey).forEach((key) => {
      urlSearchParams.delete(key);
    });

    return Object.fromEntries(urlSearchParams.entries());
  };

  return {
    query: groupQuery,
    unwrappedQuery,
    createQueryParams,
    removeFromQueryParams,
  };
};
