export interface IMoney {
  getValue: () => number;
  add: (money: IMoney) => void;
  sum: (money: IMoney[]) => void;
  substract: (money: IMoney) => void;
}

export class Money implements IMoney {
  constructor(private value: number) {}

  getValue() {
    return Number(this.value.toFixed(2));
  }

  add(money: IMoney) {
    this.value += money.getValue();
  }

  substract(money: IMoney) {
    this.value -= money.getValue();
  }

  sum(money: IMoney[]): void {
    const result = new Money(this.value);

    money.forEach((m) => {
      result.add(m);
    });
    this.value = result.getValue();
  }
}

export const formatMoney = (
  money: IMoney,
  options: Intl.NumberFormatOptions = {},
) => {
  return new Intl.NumberFormat('en-GB', {
    style: 'decimal',
    maximumFractionDigits: 2,
    useGrouping: true,
    ...options,
  }).format(Number(money.getValue()));
};

export function factory(value: number): IMoney {
  return new Money(value);
}
