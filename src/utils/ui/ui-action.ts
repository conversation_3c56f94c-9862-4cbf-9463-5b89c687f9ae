import { UnwrapRef, ref } from 'vue';

export const useUserInterfaceAction = <TData>(initialData: TData) => {
  const isPending = ref(false);
  const error = ref<Error>();
  const data = ref<TData>(initialData);

  const start = () => {
    isPending.value = true;
  };

  const resolved = (resolvedData: UnwrapRef<TData>) => {
    isPending.value = false;
    data.value = resolvedData;
    error.value = undefined;
  };

  const rejected = (rejectedError: Error, resetDataToInitial = true) => {
    if (resetDataToInitial) data.value = initialData as UnwrapRef<TData>;
    isPending.value = false;
    error.value = rejectedError;
  };

  return { start, resolved, rejected, isPending, error, data };
};
