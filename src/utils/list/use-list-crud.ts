import { Ref, computed, ref } from 'vue';
import { v4 as uuidv4 } from 'uuid';

const getUUID = () => uuidv4();

interface ObjectLike extends Record<string, any> {}

export type ListElement<T extends ObjectLike> = T & {
  key: string;
};

type List<T extends ObjectLike> = ListElement<T>[];

export const useListCrud = <TElement extends ObjectLike>(
  initialList: TElement[],
) => {
  const $initialList = initialList;
  const listWithUUID = initialList.map((item) => ({
    ...item,
    key: getUUID(),
  }));
  const list = ref<List<TElement>>(listWithUUID) as Ref<List<TElement>>;
  const isModified = ref(false);

  const add = (item: TElement) => {
    list.value.push({ ...item, key: getUUID() });
    isModified.value = true;
  };

  const edit = (
    itemToEdit: ListElement<TElement>,
    key?: ListElement<TElement>['key'],
  ) => {
    const itemKey = key ?? itemToEdit.key;
    const foundIndex = list.value.findIndex((item) => item.key === itemKey);

    list.value[foundIndex] = {
      ...itemToEdit,
      key: itemKey,
    };
    isModified.value = true;
  };

  const remove = (key: ListElement<TElement>['key']) => {
    list.value = list.value.filter((item) => item.key !== key);
    isModified.value = true;
  };

  const _setIsModified = (val: boolean) => (isModified.value = val);

  const store = (arr: TElement[]) => {
    _setIsModified(false);
    list.value = arr.map((item) => ({
      ...item,
      key: getUUID(),
    }));
  };

  const reset = () => {
    _setIsModified(false);
    list.value = $initialList.map((item) => ({
      ...item,
      key: getUUID(),
    }));
  };

  const originalList = computed((): Omit<ListElement<TElement>, 'key'>[] => {
    return list.value.map(({ key, ...rest }) => rest);
  });

  return {
    list,
    originalList,
    store,
    reset,
    isModified,
    crud: {
      add,
      edit,
      remove,
    },
  };
};
