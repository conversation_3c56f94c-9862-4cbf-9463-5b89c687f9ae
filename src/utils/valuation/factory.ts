import { Nullable } from '@/types/Common';
import { Money } from '@/utils/money';
import { DateTime } from '@/utils/dateTime';
import { ValuationType } from '@modules/factfind/types';
import { Valuation } from '@modules/factfind/models';

export function factory(
  amount: Nullable<number>,
  date: Nullable<string | Date>,
  valuationType: Nullable<ValuationType>,
): Valuation | null {
  if (!amount || !date || !valuationType) {
    return null;
  }

  return {
    amount: new Money(+amount),
    date: new DateTime(date),
    type: valuationType,
  };
}
