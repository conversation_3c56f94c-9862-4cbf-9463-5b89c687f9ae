import { onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { usePagination } from './use-pagination';
import { useQueryParams } from '@/utils/query-params/use-query-params';
import {
  type Filter,
  type FilterRulesType,
  useTableFilters,
} from './use-table-filters';

interface Config<TFilter extends Filter = Record<string, never>> {
  initialPage?: number;
  filter?: Partial<TFilter>;
  filterRules?: FilterRulesType<keyof Filter>;
  totalItems: number;
  perPage: number;
}

interface ChainOfActions<TFilter extends Filter = Record<string, never>> {
  changePage: (page: number) => ChainOfActions<TFilter>;
  addFilter: (filter: Partial<TFilter>) => ChainOfActions<TFilter>;
  resetFilters: (filterNames: Array<keyof TFilter>) => ChainOfActions<TFilter>;
  apply: () => Promise<void>;
}

export const calculatePagesAmount = (
  perPage: number,
  itemsAmount: number,
): number => {
  return Math.ceil(itemsAmount / perPage);
};

export const useTableService = <TFilter extends Filter = Record<string, never>>(
  identifier: string,
  config: Config<TFilter>,
) => {
  const totalItems = ref(config.totalItems);
  const filtersQueryParams = useQueryParams(`${identifier}_filters`);
  const tableFiltersHook = useTableFilters<TFilter>(config?.filter);
  const pageQueryParams = useQueryParams(identifier);
  const paginationHook = usePagination({
    initialPage: pageQueryParams.unwrappedQuery.value.page
      ? Number(pageQueryParams.unwrappedQuery.value.page)
      : config?.initialPage,
    pagesAmount: calculatePagesAmount(config.perPage, totalItems.value),
  });
  const router = useRouter();

  const changePage = (page: number): ChainOfActions<TFilter> => {
    paginationHook.setPage(page);

    return chain;
  };

  const applyFilterRules = (payload) => {
    const [[name, value]] = Object.entries(payload);
    if (value) {
      (config?.filterRules?.get(name) || []).forEach((rule) => {
        const [[param, fn]] = Object.entries(rule);
        const _filters = {};
        if (fn) {
          _filters[param] = fn();
        }
        addFilter(_filters);
      });
    }
  };

  const addFilter = (filter: Partial<TFilter>): ChainOfActions<TFilter> => {
    tableFiltersHook.addFilters(filter);
    return chain;
  };

  const resetFilters = (filterNames: Array<keyof TFilter>) => {
    tableFiltersHook.reset(filterNames);

    return chain;
  };

  const apply = async () => {
    const page = paginationHook.page.value;
    const filters = tableFiltersHook.filters.value;
    const query = {
      ...pageQueryParams.createQueryParams([['page', page.toString()]]),
      ...filtersQueryParams.createQueryParams(
        Object.entries(filters)
          .filter((entry) => entry[1])
          .map(([key, value]) => {
            return [key, value.toString()];
          }),
      ),
    };

    await router.replace({ query });
  };

  const chain: ChainOfActions<TFilter> = {
    changePage,
    addFilter,
    resetFilters,
    apply,
  };

  watch(totalItems, () => {
    paginationHook.setPagesAmount(
      calculatePagesAmount(config.perPage, totalItems.value),
    );
  });

  onMounted(async () => {
    const filter = Object.keys(filtersQueryParams.unwrappedQuery.value).length
      ? (filtersQueryParams.unwrappedQuery.value as TFilter)
      : config.filter;
    if (filter) {
      tableFiltersHook.addFilters(filter);
    }
    await apply();
  });

  return {
    changePage,
    addFilter,
    resetFilters,
    applyFilterRules,
    page: paginationHook.page,
    filters: tableFiltersHook.filters,
    setTotalItems: (itemsAmount: number) => (totalItems.value = itemsAmount),
    config: {
      ...config,
      totalItems,
    },
  };
};
