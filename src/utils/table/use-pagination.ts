import { computed, ref } from 'vue';

interface Props {
  initialPage?: number;
  pagesAmount: number;
}

function paginate(page: number, pagesAmount: number) {
  const items: Array<number | null> = [1];
  const range = 2;

  if (page === 1 && pagesAmount === 1) return { items };

  if (page > range && page - range > 2) items.push(null);

  const start = page - range > 2 ? page - range : 2;
  const end = Math.min(pagesAmount, page + range);

  for (let i = start; i <= end; i++) {
    items.push(i);
  }

  if (end + 1 < pagesAmount) items.push(null);

  if (end < pagesAmount) items.push(pagesAmount);

  return { items };
}

export const usePagination = ({ initialPage = 1, ...props }: Props) => {
  const page = ref<number>(initialPage);
  const pageComputed = computed(() => page.value);
  const pagesAmount = ref(props.pagesAmount);

  const prevPage = () => setPage(page.value - 1);
  const nextPage = () => setPage(page.value + 1);
  const setPage = (requestedPage: number) => (page.value = requestedPage);
  const hasNextPage = computed(() => {
    return page.value < pagesAmount.value;
  });
  const hasPrevPage = computed(() => page.value > 1);
  const isCurrentPage = (requestedPage: number): boolean => {
    return page.value === requestedPage;
  };

  return {
    nextPage,
    prevPage,
    setPage,
    hasNextPage,
    hasPrevPage,
    page: pageComputed,
    isCurrentPage,
    setPagesAmount: (amount: number) => {
      pagesAmount.value = amount;
    },
    items: computed(() => {
      return paginate(page.value, pagesAmount.value).items;
    }),
    allItems: computed(() =>
      [...Array(pagesAmount.value).keys()].map((key) => key + 1),
    ),
  };
};
