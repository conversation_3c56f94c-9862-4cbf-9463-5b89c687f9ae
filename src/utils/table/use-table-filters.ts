import { LocationQueryValueRaw } from 'vue-router';
import { ComputedRef, computed, ref } from 'vue';

export type Filter = {
  [x: string]: LocationQueryValueRaw | LocationQueryValueRaw[];
};

export type FilterRulesType<T extends string | number | symbol> = Map<
  T,
  { [k in T]?: (value?: any) => unknown }[]
>;

interface UseFilters<TFilter extends Filter> {
  reset: (filterNames: Array<keyof TFilter>) => void;
  addFilters: (newFilter: Partial<TFilter>) => void;
  filters: ComputedRef<Partial<TFilter>>;
}

type TQueryValue = LocationQueryValueRaw | LocationQueryValueRaw[];
const isArrayKey = (key: string) => !!~key.indexOf('[]');
const toArrayOfStrings = (value: TQueryValue): string[] | undefined =>
  value ? String(value).split(',') : undefined;
const castValue = (key: string, value: TQueryValue) => {
  return isArrayKey(key) ? toArrayOfStrings(value) : value;
};

const castValues = (filter: Partial<Filter>) =>
  Object.fromEntries(
    Object.entries(filter).map(([key, value]) => [key, castValue(key, value)]),
  );

export const useTableFilters = <TFilter extends Filter = Record<string, never>>(
  initialFilters?: Partial<TFilter>,
): UseFilters<TFilter> => {
  const filters = ref<Partial<Filter>>(initialFilters || {});
  const usedFilters = computed<TFilter>(() => {
    return filters.value as any;
  });

  const addFilters = (newFilter: Partial<TFilter>) => {
    filters.value = {
      ...filters.value,
      ...castValues(newFilter),
    };
  };

  const reset = async (filterNames: Array<keyof TFilter>) => {
    const newFilters = filters.value;
    for (const filterName in newFilters) {
      if (filterNames.includes(filterName)) {
        newFilters[filterName] = initialFilters
          ? (initialFilters[filterName] ?? null)
          : null;
      }
    }
    filters.value = newFilters;
  };

  return {
    addFilters,
    reset,
    filters: usedFilters,
  };
};
