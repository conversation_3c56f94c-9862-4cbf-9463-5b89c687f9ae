export const formatNumber = (
  value: number | string,
  options: Intl.NumberFormatOptions = {},
) => Number(value).toLocaleString('en', options);

export const formatPercentForModel = (
  value: number | string,
  options: Intl.NumberFormatOptions = {},
) =>
  formatNumber(value, {
    minimumFractionDigits: 2,
    ...options,
  });

export const formatPercentForView = (
  value: number | string,
  options: Intl.NumberFormatOptions = {},
) =>
  formatNumber(value, {
    minimumFractionDigits: 2,
    ...options,
    style: 'percent',
  });
