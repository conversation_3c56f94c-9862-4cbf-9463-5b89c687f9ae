import { some } from 'lodash';
import { InvalidSubmissionContext } from 'vee-validate';

export const fieldRequiredMessage = 'Field is required';
export const invalidValueMessage = 'Invalid value';
export const invalidEmailMessage = 'Must be valid email';
export const positiveNumberMessage = 'Must be a positive number';
export const invalidNumberMessage = 'Must be a valid number';
export const minLengthMessage = (fieldName: string, min = 3) =>
  `${fieldName} must at least ${min} characters long`;
export const maxLengthMessage = (fieldName: string, max = 10000) =>
  `${fieldName} must be at most ${max} characters long`;
export const invalidCharacterMessage = "Can't include []<>%$\\/";
export const emailOrReasonMessage =
  'Email is required unless a reason is provided';
export const reasonOrEmailMessage =
  'Reason is required when email is not provided';
export const emailAndReasonMessage =
  'Either email or reason must be provided, not both';
export const alphaNumericMessage =
  'Must contain only letters, numbers, spaces and hyphens';
export const futureDateMessage = 'Date must be in the future';
export const noFutureDateMessage = 'Cannot be a future date';

export const throwForValidationKey = (
  errors: InvalidSubmissionContext['errors'],
  key: string,
  msg?: string,
) => {
  if (some(Object.keys(errors), (errorKey) => errorKey.startsWith(key))) {
    throw new Error(msg || errors[key] || 'Validation error.');
  }
};
