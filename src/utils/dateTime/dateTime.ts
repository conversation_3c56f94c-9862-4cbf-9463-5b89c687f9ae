import { formatDuration, intervalToDuration, isPast } from 'date-fns';
import {
  formatForForm,
  formatWithMonthName,
  formatWithShortenedMonthName,
} from './format';

export type DateLike = Date | IDateTime | { date: string } | string;

type DateTimeCompareAction =
  | 'is-before'
  | 'is-before-or-now'
  | 'is-after'
  | 'is-after-or-now';

export interface IDateTime {
  formatToView(shortened?: boolean): string;
  formatToViewWithTime(): string;
  formatForForm(): string;
  formatWithShortName(): string;
  hasTruthyValue(): boolean;
  fromNow(): string;
  compare(action: DateTimeCompareAction, date: Date): boolean;
  valueOf(): Date;
}

type DateCompareFunction = (first: Date, second: Date) => boolean;

function isBefore(firstDate: Date, secondDate: Date) {
  return firstDate < secondDate;
}

function isBeforeOrNow(firstDate: Date, secondDate: Date) {
  return firstDate <= secondDate;
}

function isAfter(firstDate: Date, secondDate: Date) {
  return firstDate > secondDate;
}

function isAfterOrNow(firstDate: Date, secondDate: Date) {
  return firstDate >= secondDate;
}

export class DateTime implements IDateTime {
  private readonly date;

  constructor(dateStringValue: DateLike | null = null) {
    if (dateStringValue) {
      this.date =
        typeof dateStringValue !== 'string' && 'date' in dateStringValue
          ? new Date(dateStringValue.date)
          : new Date(dateStringValue as Date | string);
    }
  }

  valueOf(): Date {
    return this.date;
  }

  formatToView(): string {
    return this.date ? formatWithMonthName(this.date) : '';
  }

  formatToViewWithTime(): string {
    return Intl.DateTimeFormat('en-GB', {
      month: '2-digit',
      year: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    }).format(this.date);
  }

  formatWithShortName(): string {
    return this.date ? formatWithShortenedMonthName(this.date) : '';
  }

  formatForForm(): string {
    return this.date ? formatForForm(this.date) : '';
  }

  hasTruthyValue(): boolean {
    return !!this.date;
  }

  compare(action: DateTimeCompareAction, date: Date): boolean {
    if (!this.date) throw new Error(`DateTime object is ${this.date}`);

    const actionsMap = new Map<DateTimeCompareAction, DateCompareFunction>([
      ['is-after', isAfter],
      ['is-after-or-now', isAfterOrNow],
      ['is-before', isBefore],
      ['is-before-or-now', isBeforeOrNow],
    ]);

    const compareFunction = actionsMap.get(action) as DateCompareFunction;
    return compareFunction(this.date, date);
  }

  fromNow(): string {
    return isPast(this.date)
      ? formatDuration(
          intervalToDuration({ start: this.date, end: new Date() }),
          {
            format: ['months', 'days', 'hours'],
          },
        )
      : '';
  }
}

export function factory(dateStringValue: DateLike | null = null): IDateTime {
  return new DateTime(dateStringValue);
}
