import { DateTime as luxon } from 'luxon';
import { DateTime } from './dateTime';
import { Maybe } from '@/types/Common';

const getMonthNumber = (date: Date): number => {
  return date.getMonth() + 1;
};

export const format = (date: Date, format: string) =>
  luxon.fromJSDate(date).toFormat(format);

export const formatWithMonthName = (date: Date): string =>
  format(date, 'd LLLL y');

export const formatWithShortenedMonthName = (date: Date): string =>
  format(date, 'd LLL y');

const formatYear = (year: number) => {
  if (year < 10) {
    return `000${year}`;
  } else if (year < 100) {
    return `00${year}`;
  } else if (year < 1000) {
    return `0${year}`;
  }
  return year;
};

export const formatForForm = (date: Date): string => {
  const dateDay = date.getDate();
  const dateMonth = getMonthNumber(date);
  const year = date.getFullYear();

  const day = dateDay < 10 ? `0${dateDay}` : dateDay;
  const month = dateMonth < 10 ? `0${dateMonth}` : dateMonth;
  return `${formatYear(year)}-${month}-${day}`;
};

export const formatDateStringForView = (date: Maybe<string>) => {
  return date ? new DateTime(date).formatToView() : null;
};

export const formatDatetimeStringForView = (date: Maybe<string>) => {
  return date ? new DateTime(date).formatToViewWithTime() : null;
};
