import { defineStore } from 'pinia';
import { Nullable } from '@/types/Common';
import { DateTime, IDateTime } from '@/utils/dateTime';
import { ClientId } from '@modules/clients';
import { ActiveClient, getActiveClients } from '@modules/refdata';

export interface ActiveClientListState {
  clients: ActiveClient[];
  fetchedTime: IDateTime | null;
  status: {
    error: Error | null;
    pending: boolean;
  };
}

interface Actions {
  fetchAll: () => Promise<void>;
  newClientAdded: () => void;
}

type Getters = {
  list: () => ActiveClient[];
  getById(
    state: ActiveClientListState,
  ): (id: ClientId) => Nullable<ActiveClient>;
};

export const useActiveClients = defineStore<
  'active-clients',
  ActiveClientListState,
  Getters,
  Actions
>('active-clients', {
  state: () => ({
    clients: [],
    fetchedTime: null,
    status: {
      error: null,
      pending: false,
    },
  }),
  actions: {
    async fetchAll(): Promise<void> {
      if (this.clients.length) return;

      try {
        this.status.pending = true;

        this.clients = await getActiveClients();

        this.fetchedTime = new DateTime(new Date().toString());
        this.status.pending = false;
      } catch (e: unknown) {
        if (e instanceof Error) {
          this.status.error = e;
        } else {
          this.status.error = new Error(
            'Something went wrong while fetching active clients.',
          );
        }
      }
    },
    newClientAdded(): void {
      this.clients = [];
    },
  },

  getters: {
    list(): ActiveClient[] {
      return this.clients;
    },
    getById(state: ActiveClientListState) {
      return (id: ClientId): ActiveClient | null => {
        return (
          state.clients.find((client: ActiveClient) => client.id === id) || null
        );
      };
    },
  },
});
