import Ably from 'ably';

export default {
  install(Vue, ablyOptions = {}) {
    const options =
      typeof ablyOptions === 'function' ? ablyOptions() : ablyOptions;

    try {
      const ably = new Ably.Realtime({
        // defaults
        ...{
          authUrl: '/api/v1/user/rt',
          autoConnect: true,
          recover: function (_, cb) {
            cb(true);
          },
          idempotentRestPublishing: true,
          echoMessages: false,
        },
        ...options,
      });
    } catch (e) {
      console.error('Ably failed to initialize', e);
    }
  },
};
