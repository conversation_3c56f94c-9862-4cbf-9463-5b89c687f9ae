import { App, Ref } from 'vue';
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core';

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $breakpoints: { [key: string]: Ref<boolean> };
  }
}

export const breakpoints = {
  install: (app: App) => {
    const breakpoints = useBreakpoints(breakpointsTailwind);

    const sm = breakpoints.smaller('sm');
    const sme = breakpoints.smallerOrEqual('sm');
    const md = breakpoints.between('sm', 'md');
    const lg = breakpoints.between('md', 'lg');
    const lge = breakpoints.smallerOrEqual('lg');
    const xl = breakpoints.between('lg', 'xl');
    const xxl = breakpoints.between('xl', '2xl');
    const xxxl = breakpoints['2xl'];
    app.config.globalProperties.$breakpoints = {
      sm,
      sme,
      md,
      lg,
      lge,
      xl,
      xxl,
      xxxl,
    };
  },
};
