import { Schema, addMethod, string } from 'yup';

export const addCheckIsNumberValidation = () =>
  addMethod<any>(Schema, 'checkIsNumber', function (errorMessage) {
    return this.test('check-is-number', errorMessage, function (value) {
      if (isNaN(Number(value))) {
        return this.createError();
      }

      return true;
    });
  });

export const addCheckIsValidPhoneNumber = () =>
  addMethod(string, 'checkIsCorrectPhoneNumber', function (errorMessage) {
    return this.test('is-correct-phone-number', errorMessage, function (value) {
      if (value && !value.match(/^[+]*[(]?[0-9]{1,4}[)]?[-\s./0-9]*$/)) {
        return this.createError();
      }

      return true;
    });
  });

export default {
  install: async () => {
    addCheckIsNumberValidation();
    addCheckIsValidPhoneNumber();
  },
};
