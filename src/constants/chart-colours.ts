import { flatten, map, pick, shuffle, values } from 'lodash';
import { default as colours } from 'tailwindcss/colors';

/**
 * Chart Colours
 * @enum {string}
 * Enum of colours used in charts (charts.js) configuration
 **/
export const enum ChartColours {
  DARK_GREEN = '#215249',
  LIGHT_GREEN = '#c9e386',
  MID_PURPLE = '#c2aefd',
  LIGHT_GRAY = '#e4e4e7',
}

const extendedColours = [
  ...shuffle(
    flatten(
      map(
        values(pick(colours, ['red', 'yellow', 'lime', 'sky', 'purple'])),
        ($c) => values(pick($c, [200, 500, 800])),
      ),
    ),
  ),
];

export const getChartColors = () => [
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  ...Object.values(ChartColours),
  ...extendedColours,
];
