<template>
  <div class="relative w-full" style="aspect-ratio: 2/1">
    <svg
      class="size-full"
      viewBox="0 0 120 55"
      preserveAspectRatio="xMidYMid meet"
    >
      <!-- Tick marks -->
      <g
        v-for="i in 41"
        :key="i"
        :transform="`rotate(${(i - 1) * 6 - 120} 60 40)`"
      >
        <line
          x1="60"
          y1="10"
          x2="60"
          y2="16"
          :stroke="getTickColor(i - 1, 41)"
          stroke-width="1.5"
          stroke-linecap="round"
        />
      </g>

      <!-- Score text -->
      <text
        x="60"
        y="38"
        text-anchor="middle"
        dominant-baseline="middle"
        font-size="14"
        font-weight="bold"
        fill="#4E2D95"
      >
        {{ Math.round(animatedScore) }}
      </text>
      <text
        x="60"
        y="50"
        text-anchor="middle"
        dominant-baseline="middle"
        font-size="8"
        fill="#6B7280"
      >
        /{{ maxScore }}
      </text>
    </svg>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue';

  const props = withDefaults(
    defineProps<{
      score: number;
      maxScore?: number;
    }>(),
    {
      maxScore: 100,
    },
  );

  const animatedScore = ref(0);

  const getTickColor = (tickIndex: number, totalTicks: number) => {
    const progress = animatedScore.value / props.maxScore;
    const tickProgress = tickIndex / (totalTicks - 1);

    if (tickProgress <= progress) {
      const gradientProgress = tickProgress / progress;
      // eslint-disable-next-line id-length
      const r = Math.round(194 + (78 - 194) * gradientProgress);
      // eslint-disable-next-line id-length
      const g = Math.round(174 + (45 - 174) * gradientProgress);
      // eslint-disable-next-line id-length
      const b = Math.round(253 + (149 - 253) * gradientProgress);
      return `rgb(${r}, ${g}, ${b})`;
    } else {
      return '#E5E7EB';
    }
  };

  onMounted(() => {
    const duration = 1500;
    const startTime = Date.now();
    const targetScore = props.score;

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);

      animatedScore.value = targetScore * easeOutQuart;

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    animate();
  });
</script>
