<template>
  <span :class="`material-icons${iconType}`">
    {{ iconCode }}
  </span>
</template>

<script setup lang="ts">
  import { computed } from 'vue';

  const props = defineProps<{
    icon: string;
    type?: 'outlined' | 'round' | 'sharp';
  }>();

  const iconCode = computed(() => String(props.icon).toLowerCase());
  const iconType = computed(() => (props.type ? `-${props.type}` : ''));
</script>
