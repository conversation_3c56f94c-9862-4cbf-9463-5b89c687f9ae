<template>
  <clean-select-field
    ref="cleanSelect"
    v-model="inputValue"
    :name="name"
    :label="label"
    :groups="groups"
    :options="options"
    :disabled="disabled"
    :has-error="!!errorMessage"
    :searchable="props.searchable"
    :no-results-text="props.noResultsText"
    :hide-disabled-options="hideDisabledOptions"
    :error-message="errorMessage"
    :is-busy="isBusy"
    :is-busy-text="isBusyText"
    :placeholder="placeholder"
    :can-deselect="canDeselect"
    :can-clear="canClear"
    :warning-message="warningMessage"
    :show-warning="showWarning"
    @on-select="handleSelect"
  >
    <slot />
  </clean-select-field>
</template>

<script setup lang="ts">
  import { ref, toRef, watch } from 'vue';
  import { useField } from 'vee-validate';
  import { SelectOption, SelectOptionGroup } from './field-model';
  import { CleanSelectField } from './clean-fields';
  interface Props {
    label: string;
    value?: number | string;
    name: string;
    autocomplete?: string;
    groups?: boolean;
    options: Array<SelectOption | SelectOptionGroup>;
    disabled?: boolean;
    hideDisabledOptions?: boolean;
    searchable?: boolean;
    noResultsText?: string;
    isBusy?: boolean;
    isBusyText?: string;
    placeholder?: string;
    canDeselect?: boolean;
    canClear?: boolean;
    warningMessage?: string;
    showWarning?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    value: undefined,
    autocomplete: '',
    disabled: false,
    hideDisabledOptions: false,
    searchable: true,
    noResultsText: '',
    isBusy: false,
    isBusyText: '',
    placeholder: '',
    canDeselect: true,
    canClear: false,
    warningMessage: '',
    showWarning: false,
  });

  const nameRef = toRef(props, 'name');
  const propsValue = toRef(props, 'value');
  const emit = defineEmits(['on-select']);
  const cleanSelect = ref<InstanceType<typeof CleanSelectField> | null>(null);

  const {
    value: inputValue,
    errorMessage,
    resetField,
    handleChange,
  } = useField(nameRef, undefined, {
    initialValue: props.value,
  });

  const focusSearch = () => cleanSelect.value?.focusSearch();
  const resetFieldValue = (value: number | string) => {
    return resetField({
      value,
    });
  };

  defineExpose({
    focusSearch,
    resetFieldValue,
  });

  watch(propsValue, () => {
    handleChange(propsValue.value);
  });

  const handleSelect = (selectedOptionValue?: SelectOption['value']) => {
    handleChange(selectedOptionValue);
    emit('on-select', selectedOptionValue);
  };
</script>
