<template>
  <input-wrapper
    :name="props.name"
    :error-message="errorMessage"
    :label="label"
    :is-required="isRequired"
  >
    <simple-input
      :id="name"
      type="date"
      :value="value"
      :model-value="value"
      :autocomplete="autocomplete"
      :name="name"
      :label="label"
      :placeholder="placeholder"
      :disabled="disabled"
      :is-required="isRequired"
      :error-message="errorMessage"
      class="h-9"
      @blur="handleBlur"
    />
  </input-wrapper>
</template>

<script setup lang="ts">
  import { toRef } from 'vue';
  import InputWrapper from '../InputWrapper.vue';
  import SimpleInput from './SimpleInput.vue';

  const props = defineProps<{
    value: string | null;
    autocomplete?: string;
    name: string;
    label: string;
    placeholder?: string;
    disabled?: boolean;
    isRequired?: boolean;
    errorMessage?: string;
  }>();

  const label = toRef(props, 'label');

  const emit = defineEmits(['update:modelValue', 'blur']);

  const handleBlur = (ev: Event) => {
    emit('blur', ev);
  };
</script>
