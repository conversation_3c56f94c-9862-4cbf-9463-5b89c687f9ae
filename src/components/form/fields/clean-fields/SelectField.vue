<template>
  <input-wrapper
    :warning-message="warningMessage"
    :show-warning="showWarning"
    :name="props.name"
    :label="props.label"
    :error-message="errorMessage"
  >
    <search-select
      ref="searchSelect"
      :model-value="modelValue"
      :groups="groups"
      :options="options"
      :has-error="!!errorMessage"
      :searchable="props.searchable"
      :no-results-text="props.noResultsText"
      :no-options-text="isBusy ? isBusyText : null"
      :hide-disabled-options="hideDisabledOptions"
      :disabled="disabled"
      :placeholder="placeholder"
      :can-deselect="canDeselect"
      :can-clear="canClear"
      :show-warning="showWarning"
      @on-select="handleSelect"
    >
      <template #option="{ option }">
        <slot name="option" :option="option" />
      </template>
    </search-select>
  </input-wrapper>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import InputWrapper from '../InputWrapper.vue';
  import { SelectOption, SelectOptionGroup } from '../field-model';
  import SearchSelect from '../select/SearchSelect.vue';

  interface Props {
    label: string;
    modelValue?: number | string | null;
    name: string;
    autocomplete?: string;
    groups?: boolean;
    options: Array<SelectOption | SelectOptionGroup>;
    disabled?: boolean;
    hideDisabledOptions?: boolean;
    searchable?: boolean;
    noResultsText?: string;
    errorMessage?: string;
    isBusy?: boolean;
    isBusyText?: string;
    placeholder?: string;
    canDeselect?: boolean;
    canClear?: boolean;
    warningMessage?: string;
    showWarning?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: '',
    autocomplete: '',
    disabled: false,
    hideDisabledOptions: false,
    searchable: true,
    noResultsText: undefined,
    errorMessage: '',
    isBusy: false,
    isBusyText: 'Loading..',
    placeholder: '',
    canDeselect: true,
    canClear: false,
    warningMessage: '',
    showWarning: false,
  });

  const emit = defineEmits(['on-select']);

  const searchSelect = ref<InstanceType<typeof SearchSelect> | null>(null);
  const focusSearch = () => searchSelect.value?.focusInput();

  defineExpose({
    focusSearch,
  });

  const handleSelect = (e: any) => {
    emit('on-select', e);
  };
</script>
