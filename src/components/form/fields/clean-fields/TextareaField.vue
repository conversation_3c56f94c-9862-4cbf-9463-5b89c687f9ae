<template>
  <input-wrapper
    :name="props.name"
    :error-message="errorMessage"
    :label="label"
    :is-required="isRequired"
  >
    <textarea
      :id="name"
      :value="modelValue || ''"
      :name="name"
      type="text"
      :autocomplete="autocomplete"
      :disabled="disabled"
      :placeholder="placeholder"
      :rows="rows"
      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary disabled:cursor-not-allowed disabled:bg-gray-100 disabled:text-gray-400 disabled:shadow-none sm:text-sm lg:text-base"
      :class="{
        'border-red-600': !!errorMessage,
        'text-red-600': !!errorMessage,
      }"
      :readonly="!!isReadonly"
      @change="handleChange"
      @blur="$emit('blur')"
      @focus="$emit('focus')"
      @focusout="$emit('focusout')"
    ></textarea>
  </input-wrapper>
</template>

<script setup lang="ts">
  import InputWrapper from '../InputWrapper.vue';

  const props = defineProps<{
    id: string;
    modelValue: string | number | null;
    autocomplete?: string;
    name: string;
    label: string;
    placeholder?: string;
    disabled?: boolean;
    isRequired?: boolean;
    isReadonly?: boolean;
    errorMessage?: string;
    rows: string;
  }>();

  const emit = defineEmits(['update:modelValue', 'blur', 'focus', 'focusout']);

  const handleChange = (event: Event) => {
    emit('update:modelValue', (event.target as HTMLTextAreaElement).value);
  };
</script>
