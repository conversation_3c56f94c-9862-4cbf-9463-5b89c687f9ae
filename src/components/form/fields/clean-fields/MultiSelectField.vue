<template>
  <input-wrapper
    :name="props.name"
    :label="label || ''"
    :error-message="errorMessage"
  >
    <multiselect
      :model-value="props.modelValue"
      mode="tags"
      :classes="{
        wrapper: `multiselect-wrapper ${
          disabled
            ? '!cursor-not-allowed bg-gray-100 text-gray-400'
            : 'cursor-pointer'
        }`,
        container: `text-base lg:leading-none ${
          hasError
            ? 'outline-red-600 border-red-600 outline-1 text-red-600'
            : ''
        } bg-white relative mx-auto w-full rounded-md flex items-center justify-end border border-gray-300`,
        containerActive: 'outline outline-2 outline-primary',
        containerDisabled: 'text-gray-400 bg-gray-100',
        dropdown:
          'max-h-60 -mb-1 absolute -left-px -right-px bottom-0 transform translate-y-full border border-gray-300 -mt-px overflow-y-auto z-50 bg-white flex flex-col rounded-lg p-1 shadow',
        dropdownHidden: 'hidden',
        option:
          'text-black flex items-center justify-start box-border text-left cursor-pointer text-base leading-snug py-2 px-4 rounded-lg',
        optionPointed: 'text-gray-800 bg-[#F1F0F9]',
        search:
          'w-full absolute inset-0 outline-none focus:ring-0 appearance-none box-border border-0 text-base font-sans bg-white rounded pl-3.5 rtl:pl-0 rtl:pr-3.5',
        caret: `multiselect-caret ${hasError ? 'right-5' : ''} ${disabled ? 'hidden' : ''}`,
        optionDisabled: hideDisabledOptions ? 'hidden' : '',
        tagsSearchWrapper:
          'inline-block relative mx-1 flex-grow flex-shrink h-full',
        tagsSearch:
          'absolute inset-0 border-0 outline-none focus:ring-0 appearance-none p-0 text-base font-sans box-border w-full',
        tags: 'flex-grow flex-shrink flex flex-wrap items-center pl-2 rtl:pl-0 rtl:pr-2',
      }"
      :disabled="disabled"
      :aria-disabled="disabled"
      :hide-selected="true"
      :hide-disabled-options="hideDisabledOptions"
      :options="options"
      :searchable="true"
      :can-clear="props.canClear"
      :can-deselect="canDeselect ?? true"
      :no-results-text="props.noResultsText || 'No results found.'"
      :no-options-text="props.noOptionsText"
      :placeholder="placeholder"
      @change="handleChange"
    >
      <template
        #tag="{
          option,
          handleTagRemove,
        }: {
          option: {
            label: string;
            disabled: boolean;
          };
          handleTagRemove: (option: any, event: any) => void;
        }"
      >
        <div
          class="m-1 flex flex-row rounded-2xl p-2"
          :class="`${props.disabled ? 'bg-gray-200/50' : 'bg-gray-100'}`"
        >
          {{ option.label }}
          <span
            v-if="!option.disabled"
            class="self-center"
            @click="handleTagRemove(option, $event)"
          >
            <XMarkIcon
              class="ml-1 size-4 cursor-pointer rounded-full hover:bg-[#d3d3d8]"
            />
          </span>
        </div>
      </template>
    </multiselect>
    <span
      v-if="props.hint && !errorMessage"
      class="flex py-1 text-xs leading-tight text-gray-500"
      >Hint: {{ hint }}</span
    >
  </input-wrapper>
</template>

<script setup lang="ts">
  import { XMarkIcon } from '@heroicons/vue/20/solid';
  import Multiselect from '@vueform/multiselect';
  import InputWrapper from '../InputWrapper.vue';
  import { SelectOption } from '../field-model';

  const props = defineProps<{
    options: SelectOption[];
    hasError?: boolean;
    name: string;
    searchable: boolean;
    noResultsText?: string;
    noOptionsText: string;
    hideDisabledOptions?: boolean;
    modelValue: Array<SelectOption['value']>;
    placeholder?: string;
    disabled?: boolean;
    canDeselect?: boolean;
    canClear: boolean;
    errorMessage?: string;
    label?: string;
    hint?: string;
  }>();

  const emit = defineEmits(['on-select', 'update:modelValue']);

  const handleChange = (value) => {
    value = value as Array<SelectOption['value']>;

    emit('on-select', value);
  };
</script>

<style src="@vueform/multiselect/themes/default.css"></style>
