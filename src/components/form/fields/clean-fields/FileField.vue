<template>
  <input
    ref="fileInput"
    class="hidden"
    type="file"
    :accept="accept"
    @change="handleFileUpload"
  />
  <slot name="button" :open-file-input="openFileInput"></slot>
</template>

<script setup lang="ts">
  import { ref } from 'vue';

  defineProps<{
    accept: string;
  }>();

  const emit = defineEmits<{
    (e: 'on-file-upload', files: FileList): void;
  }>();

  const fileInput = ref<HTMLInputElement | null>(null);

  const handleFileUpload = async () => {
    if (!fileInput.value || !fileInput.value.files?.length) return;
    emit('on-file-upload', fileInput.value.files);
  };

  const openFileInput = () => {
    if (!fileInput.value) return;
    // clear input before uploading so that it will replace file with the same name as previous
    fileInput.value.value = '';
    fileInput.value.click();
  };
</script>
