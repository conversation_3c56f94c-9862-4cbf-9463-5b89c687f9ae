<template>
  <multi-state-switch
    :options="options"
    :label="label"
    :name="name"
    :model-value="inputValue"
    :error-message="errorMessage"
    :disabled="disabled"
    @update:model-value="handleChange"
  />
</template>

<script setup lang="ts">
  import { toRef } from 'vue';
  import { SelectOption } from './field-model';
  import { MultiStateSwitch } from './clean-fields';
  import { useField } from 'vee-validate';

  const props = withDefaults(
    defineProps<{
      options: SelectOption[];
      name: string;
      disabled?: boolean;
      value?: SelectOption['value'];
      label?: string;
    }>(),
    {
      disabled: false,
      value: undefined,
      label: '',
    },
  );

  const name = toRef(props, 'name') as unknown as string;

  defineEmits(['update:modelValue']);

  const {
    value: inputValue,
    errorMessage,
    handleChange,
  } = useField(name, undefined, {
    initialValue: props.value,
  });
</script>
