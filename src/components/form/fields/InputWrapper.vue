<template>
  <div
    class="mb-4"
    :class="{
      'flex items-center gap-4': props.isHorizontal,
      'justify-between': props.justifyBetween,
    }"
  >
    <label
      :for="name"
      class="mb-1 block font-sans font-medium sm:text-sm lg:text-base"
      :class="{
        'text-red-600': !!props.errorMessage,
        'text-gray-400': props.isDisabled,
        'text-gray-700': !props.errorMessage && !props.isDisabled,
        '!mb-0': props.isHorizontal,
      }"
      >{{ props.label }}{{ isRequired ? '*' : '' }}</label
    >
    <div class="relative" :class="contentClass">
      <slot />
      <div
        v-show="props.errorMessage"
        class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3"
      >
        <exclamation-circle-icon
          class="size-5 text-red-500"
          aria-hidden="true"
        />
      </div>
    </div>
    <div v-show="props.errorMessage" class="mt-2">
      <p class="text-sm text-red-600">
        {{ props.errorMessage }}
      </p>
    </div>
    <div
      v-show="showWarning && warningMessage"
      class="mt-2 flex flex-row gap-1"
    >
      <exclamation-triangle-icon
        class="size-5 text-yellow-500"
        aria-hidden="true"
      />
      <p class="text-sm text-yellow-500">
        {{ warningMessage }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    ExclamationCircleIcon,
    ExclamationTriangleIcon,
  } from '@heroicons/vue/20/solid';
  import { toRef } from 'vue';

  interface Props {
    name: string;
    label: string;
    errorMessage?: string | null;
    warningMessage?: string;
    showWarning?: boolean;
    isRequired?: boolean;
    contentClass?: string;
    isDisabled?: boolean;
    isHorizontal?: boolean;
    justifyBetween?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    errorMessage: null,
    contentClass: '',
    isRequired: false,
    warningMessage: '',
    showWarning: false,
    isDisabled: false,
    isHorizontal: false,
    justifyBetween: false,
  });

  const name = toRef(props, 'name');
</script>

<style scoped></style>
