<template>
  <clean-text-field
    :id="name"
    :value="inputValue ?? null"
    :model-value="inputValue ?? null"
    :autocomplete="autocomplete"
    :name="name"
    :label="label"
    :placeholder="placeholder"
    :hint="hint"
    :disabled="disabled"
    :is-required="isRequired"
    :error-message="errorMessage"
    :type="type"
    :is-readonly="isReadonly"
    @input="handleInput"
    @blur="handleBlur"
  />
</template>

<script setup lang="ts">
  import { toRef } from 'vue';
  import { useField } from 'vee-validate';
  import { CleanTextField } from './clean-fields';

  const props = defineProps({
    type: {
      type: String,
      default: '',
    },
    value: {
      type: String,
      default: undefined,
    },
    autocomplete: {
      type: String,
      default: '',
      required: false,
    },
    name: {
      type: String,
      required: true,
    },
    label: {
      type: String,
      required: true,
    },
    placeholder: {
      type: String,
      default: '',
    },
    hint: {
      type: String,
      default: undefined,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    isRequired: {
      type: Boolean,
      default: false,
      required: false,
    },
    isReadonly: {
      type: Boolean,
      default: false,
      required: false,
    },
  });

  const nameRef = toRef(props, 'name');

  const {
    value: inputValue,
    errorMessage,
    handleBlur,
    handleChange,
  } = useField(nameRef, undefined, {
    initialValue: props.value,
  });

  const emit = defineEmits(['on-change']);
  const handleInput = ($event) => {
    handleChange($event.target.value);
    emit('on-change', inputValue);
  };
</script>
