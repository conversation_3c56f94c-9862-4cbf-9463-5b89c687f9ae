<template>
  <multi-select-field
    v-model="inputValue"
    :options="options"
    :has-error="hasError"
    :name="name"
    :searchable="searchable"
    :no-results-text="noResultsText"
    :no-options-text="noOptionsText"
    :hide-disabled-options="hideDisabledOptions"
    :placeholder="placeholder"
    :disabled="disabled"
    :can-deselect="true"
    :can-clear="canClear"
    :error-message="errorMessage"
    :label="label"
    :hint="hint"
    @on-select="handleSelect"
  />
</template>

<script setup lang="ts">
  import { toRef, watch } from 'vue';
  import { SelectOption } from './field-model';
  import { MultiSelectField } from './clean-fields';
  import { useField } from 'vee-validate';

  const props = withDefaults(
    defineProps<{
      options: SelectOption[];
      hasError?: boolean;
      name: string;
      searchable?: boolean;
      noResultsText?: string;
      noOptionsText?: string;
      hideDisabledOptions?: boolean;
      value?: Array<SelectOption['value']>;
      placeholder?: string;
      disabled?: boolean;
      canDeselect?: boolean;
      canClear?: boolean;
      errorMessage?: string;
      label?: string;
      hint?: string;
    }>(),
    {
      searchable: false,
      noOptionsText: '',
      canClear: false,
      noResultsText: '',
      placeholder: '',
      errorMessage: '',
      label: '',
      hint: undefined,
      value: undefined,
    },
  );

  const nameRef = toRef(props, 'name');
  const propsValue = toRef(props, 'value');
  const emit = defineEmits(['on-select']);

  const {
    value: inputValue,
    errorMessage,
    handleChange,
  } = useField(nameRef, undefined, {
    initialValue: props.value,
  });

  watch(propsValue, () => (inputValue.value = props.value ?? []));

  const handleSelect = (selectedOptionValue: SelectOption['value']) => {
    handleChange(selectedOptionValue);
    emit('on-select', selectedOptionValue);
  };
</script>
