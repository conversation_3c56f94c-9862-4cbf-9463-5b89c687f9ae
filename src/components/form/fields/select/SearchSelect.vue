<template>
  <multiselect
    ref="input"
    :model-value="props.modelValue"
    :classes="{
      wrapper: `multiselect-wrapper ${
        disabled
          ? '!cursor-not-allowed bg-gray-100 text-gray-400'
          : 'cursor-pointer'
      }`,
      container: `text-base lg:leading-none ${
        props.hasError
          ? 'outline-red-600 border-red-600 outline-1 text-red-600'
          : ''
      } 
      ${
        props.showWarning
          ? 'outline-yellow-500 border-yellow-500 outline-1 text-yellow-500'
          : ''
      }
      bg-white relative mx-auto w-full rounded-md flex items-center justify-end border border-gray-300`,
      containerActive: 'outline outline-2 outline-primary',
      containerDisabled: 'text-gray-400 bg-gray-100',
      dropdown:
        'max-h-60 -mb-1 absolute -left-px -right-px bottom-0 transform translate-y-full border border-gray-300 -mt-px overflow-y-auto z-50 bg-white flex flex-col rounded-lg p-1 shadow',
      dropdownHidden: 'hidden',
      option:
        'text-black flex items-center justify-start box-border text-left text-base leading-snug py-2 px-4 rounded-lg',
      optionPointed: 'cursor-pointer text-gray-800 bg-[#F1F0F9]',
      search:
        'w-full absolute inset-0 outline-none focus:ring-0 appearance-none box-border border-0 text-base font-sans bg-white rounded pl-3.5 rtl:pl-0 rtl:pr-3.5',
      caret: `multiselect-caret ${props.hasError ? 'right-5' : ''} ${disabled ? 'hidden' : ''}`,
      optionSelected: 'is-selected bg-primary-100',
      optionSelectedPointed: `is-selected is-pointed ${
        props.canDeselect ? 'cursor-pointer' : 'cursor-not-allowed'
      } bg-primary-100`,
      optionSelectedDisabled:
        'is-selected is-disabled bg-primary-100 cursor-not-allowed',
      optionDisabled: `${
        hideDisabledOptions ? 'hidden' : ''
      } cursor-not-allowed text-gray-400`,
      groupLabel:
        'flex text-sm box-border items-center justify-start text-left py-1 px-3 font-semibold cursor-default leading-normal',
    }"
    :groups="groups"
    :options="options"
    :searchable="props.searchable"
    :can-clear="props.canClear"
    :can-deselect="canDeselect"
    :no-results-text="props.noResultsText || 'No results found.'"
    :no-options-text="props.noOptionsText as string"
    :disabled="disabled"
    :placeholder="placeholder"
    @change="handleChange"
  >
    <template #option="{ option }">
      <slot name="option" :option="option" />
    </template>
  </multiselect>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import Multiselect from '@vueform/multiselect';
  import { SelectOption, SelectOptionGroup } from '../field-model';

  const props = defineProps<{
    options: Array<SelectOption | SelectOptionGroup>;
    groups?: boolean;
    hasError?: boolean;
    searchable: boolean;
    noResultsText?: string;
    noOptionsText: string | null;
    hideDisabledOptions?: boolean;
    modelValue: SelectOption['value'] | null;
    placeholder?: string;
    disabled?: boolean;
    canDeselect: boolean;
    canClear: boolean;
    showWarning?: boolean;
  }>();

  const emit = defineEmits(['on-select', 'update:modelValue']);

  const input = ref<HTMLInputElement | null>(null);
  const focusInput = () => input.value?.focus();

  defineExpose({
    focusInput,
  });

  const handleChange = (value) => {
    value = value as SelectOption['value'];
    emit('on-select', value);
  };
</script>

<style src="@vueform/multiselect/themes/default.css"></style>
