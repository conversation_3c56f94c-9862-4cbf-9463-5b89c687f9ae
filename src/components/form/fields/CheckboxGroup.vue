<template>
  <input-wrapper
    :name="props.name"
    :label="label"
    :error-message="errorMessage"
  >
    <div class="flex flex-col gap-4 pt-4">
      <template v-if="groups">
        <div
          v-for="(group, index) in options as SelectOptionGroup[]"
          :key="index"
        >
          <h6 class="text-sm leading-8 text-gray-500">{{ group.label }}</h6>
          <div class="flex flex-col gap-1">
            <div
              v-for="option in group.options as SelectOption[]"
              :key="String(option.value)"
              class="flex items-center"
            >
              <input
                :id="String(option.value)"
                :name="props.name"
                type="checkbox"
                :value="option.value"
                :checked="inputValues.includes(option.value)"
                :disabled="option.disabled"
                class="size-4 text-primary focus:ring-primary"
                :class="{
                  'border-red-600': !!errorMessage,
                  'text-red-600': !!errorMessage,
                  'bg-gray-100': option.disabled,
                }"
                @change="handleChange(option.value)"
                @blur="handleBlur"
              />
              <label
                :for="String(option.value)"
                class="ml-3 block text-sm"
                :class="
                  option.disabled
                    ? 'text-gray-400'
                    : 'text-gray-700 hover:cursor-pointer'
                "
              >
                {{ option.label }}
              </label>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <div
          v-for="option in options as SelectOption[]"
          :key="String(option.value)"
          class="flex items-center"
        >
          <input
            :id="String(option.value)"
            :name="props.name"
            type="checkbox"
            :value="option.value"
            :checked="inputValues.includes(option.value)"
            :disabled="option.disabled"
            class="size-4 text-primary focus:ring-primary"
            :class="{
              'border-red-600': !!errorMessage,
              'text-red-600': !!errorMessage,
              'bg-gray-100': option.disabled,
            }"
            @change="handleChange(option.value)"
            @blur="handleBlur"
          />
          <label
            :for="String(option.value)"
            class="ml-3 block text-sm"
            :class="
              option.disabled
                ? 'text-gray-400'
                : 'text-gray-700 hover:cursor-pointer'
            "
          >
            {{ option.label }}
          </label>
        </div>
      </template>
    </div>
  </input-wrapper>
</template>

<script setup lang="ts">
  import { toRef } from 'vue';
  import { useField } from 'vee-validate';
  import InputWrapper from './InputWrapper.vue';
  import { SelectOption, SelectOptionGroup } from './field-model';

  const props = defineProps<{
    name: string;
    label: string;
    value?: Array<SelectOption['value']>;
    groups?: boolean;
    options: Array<SelectOption | SelectOptionGroup>;
  }>();

  const name = toRef(props, 'name');

  const {
    checked,
    errorMessage,
    handleBlur,
    handleChange,
    value: inputValues,
  } = useField(name, undefined, {
    type: 'checkbox',
    checkedValue: props.value,
  });
</script>
