<template>
  <clean-text-area-field
    :id="name"
    :model-value="inputValue || ''"
    :autocomplete="autocomplete"
    :name="name"
    :label="label"
    :placeholder="placeholder"
    :disabled="disabled"
    :is-required="isRequired"
    :is-readonly="isReadonly"
    :error-message="errorMessage"
    :rows="rows"
    @input="handleChange"
    @blur="handleBlur"
  />
</template>

<script setup lang="ts">
  import { useField } from 'vee-validate';
  import { CleanTextAreaField } from './clean-fields';
  import { toRef } from 'vue';

  const props = withDefaults(
    defineProps<{
      value?: string;
      autocomplete?: string;
      name: string;
      label: string;
      placeholder?: string;
      disabled?: boolean;
      isRequired?: boolean;
      isReadonly?: boolean;
      errorMessage?: string;
      rows?: string;
    }>(),
    {
      value: undefined,
      autocomplete: '',
      placeholder: '',
      isRequired: false,
      isReadonly: false,
      errorMessage: '',
      rows: '2',
    },
  );

  const nameRef = toRef(props, 'name');

  const {
    value: inputValue,
    errorMessage,
    handleBlur,
    handleChange,
  } = useField(nameRef, undefined, {
    initialValue: props.value,
  });
</script>
