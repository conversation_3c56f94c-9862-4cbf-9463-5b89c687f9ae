import { groupBy } from 'lodash';
import { Options } from '@/types/Common';

export type SelectOption<TCtx extends object = any> = {
  value: string | number | boolean;
  label: string;
  disabled?: boolean;
  ctx?: TCtx;
};

export type SelectOptionGroup<TCtx extends object = any> = {
  label: string;
  options: SelectOption<TCtx>[];
};

export const groupOptions = (
  options: Options[],
  iteratee = 'label',
): SelectOptionGroup[] => {
  const groupedOptions = [] as SelectOptionGroup[];
  const $options = groupBy(options, iteratee);

  Object.keys($options).forEach((label) => {
    groupedOptions.push({
      label,
      // eslint-disable-next-line id-length
      options: $options[label].map((o) => ({
        label: o.name,
        value: o.id,
      })),
    });
  });
  return groupedOptions;
};
