<template>
  <div class="my-5">
    <label
      :for="name"
      class="mb-1 block font-sans font-medium sm:text-sm lg:text-base"
      :class="{
        'text-red-600': !!errorMessage,
        'text-gray-400': disabled,
        'text-gray-700': !errorMessage && !disabled,
      }"
      >{{ label }}{{ isRequired ? '*' : '' }}</label
    >
    <div class="relative mt-1 flex rounded-md shadow-sm">
      <div
        v-if="!noAppend"
        class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"
      >
        <span class="text-gray-500 sm:text-sm">&pound;</span>
      </div>
      <input
        :id="name"
        :name="name"
        :value="inputValue ?? null"
        :model-value="inputValue ?? null"
        v-maska="maskOptions"
        type="text"
        autocomplete="off"
        :disabled="disabled"
        :placeholder="placeholder"
        class="peer block w-full flex-1 rounded-md border-gray-300 bg-gray-50 disabled:bg-gray-100 disabled:text-gray-400 disabled:disabled:shadow-none group-[.text-right]:text-right sm:text-sm lg:text-base"
        :class="{
          'border-red-600': !!errorMessage,
          'text-red-600': !!errorMessage,
          'cursor-not-allowed bg-gray-100 text-gray-400': isReadonly,
          'rounded-none rounded-l-md': !noPrepend && !errorMessage,
          'pl-7': !noAppend,
          'pr-12': !noPrepend,
        }"
        @input="handleInput"
        @blur="handleBlur"
      />
      <span
        v-if="!noPrepend && !errorMessage"
        class="inline-flex items-center rounded-r-md border border-l-0 border-gray-300 bg-gray-50 px-3 text-gray-500 peer-disabled:bg-gray-100 peer-disabled:text-gray-400 peer-disabled:disabled:shadow-none sm:text-sm lg:text-base"
        >.00</span
      >
      <div class="relative">
        <div
          v-show="errorMessage"
          class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3"
        >
          <ExclamationCircleIcon
            class="size-5 text-red-500"
            aria-hidden="true"
          />
        </div>
      </div>
    </div>
    <div>
      <span
        v-if="hint && !errorMessage"
        class="flex py-1 text-xs leading-tight text-gray-500"
        >Hint: {{ hint }}</span
      >
      <div
        v-show="errorMessage"
        class="pointer-events-none inset-y-0 right-0 my-2 flex items-center pr-3"
      >
        <p class="text-sm text-red-600">
          {{ errorMessage }}
        </p>
      </div>
      <div v-if="warningMessage" class="mt-2 flex flex-row gap-1">
        <ExclamationTriangleIcon
          class="size-5 text-yellow-500"
          aria-hidden="true"
        />
        <p class="text-sm text-yellow-500">
          {{ warningMessage }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { toRef } from 'vue';
  import { MaskInputOptions } from 'maska';
  import { useField } from 'vee-validate';
  import {
    ExclamationCircleIcon,
    ExclamationTriangleIcon,
  } from '@heroicons/vue/20/solid';
  //

  const {
    name,
    label,
    value = undefined,
    hint = '',
    isRequired = false,
    isReadonly = false,
    disabled = false,
    placeholder = undefined,
    warningMessage = undefined,
    noAppend = false,
    noPrepend = false,
  } = defineProps<{
    name: string;
    label: string;
    value?: string;
    isRequired?: boolean;
    isReadonly?: boolean;
    disabled?: boolean;
    placeholder?: string;
    hint?: string;
    warningMessage?: string;
    noAppend?: boolean;
    noPrepend?: boolean;
  }>();

  const maskOptions: MaskInputOptions = {
    mask: '90',
    tokens: {
      9: {
        pattern: /[1-9]/,
      },
      0: {
        pattern: /\d/,
        multiple: true,
      },
    },
    eager: true,
  };
  defineExpose({ maskOptions });

  const nameRef = toRef(() => name);

  const {
    value: inputValue,
    errorMessage,
    handleBlur,
    handleChange,
  } = useField(nameRef, undefined, {
    initialValue: value,
  });

  const emit = defineEmits(['on-change']);
  const handleInput = ($event) => {
    handleChange($event.target.value);
    emit('on-change', inputValue);
  };
</script>
