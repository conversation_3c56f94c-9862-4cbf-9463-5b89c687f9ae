<template>
  <BaseModal :config="{ isOpened: true, outsideClickClose: false }">
    <BaseBox
      data-testid="modal-confirmation"
      class="absolute left-1/2 top-10 min-w-[70%] -translate-x-1/2 rounded text-center md:min-w-[20%]"
    >
      <BaseSection
        v-if="title"
        no-padding-y
        class="rounded-t-lg border-b border-gray-100 bg-gray-50 p-3 font-medium"
        :title="title"
      />
      <BaseSection divider="bottom" class="whitespace-pre-wrap">
        {{ message }}
      </BaseSection>
      <BaseSection
        no-padding-y
        class="flex justify-between gap-2 rounded-b-lg bg-gray-50 py-2 md:justify-end"
      >
        <BaseButton
          theme="text-like"
          @click="emit('cancel')"
          data-button-cancel
        >
          Cancel
        </BaseButton>
        <BaseButton
          theme="primary"
          @click="emit('confirm')"
          data-button-confirm
        >
          Confirm
        </BaseButton>
      </BaseSection>
    </BaseBox>
  </BaseModal>
</template>

<script setup lang="ts">
  import BaseBox from '@/components/BaseBox.vue';
  import BaseButton from '@/components/BaseButton.vue';
  import BaseModal from '@/components/BaseModal.vue';
  import BaseSection from '@/components/BaseSection.vue';

  const emit = defineEmits(['confirm', 'cancel']);
  defineProps<{ message: string; title?: string }>();
</script>
