<template>
  <ul data-testid="password-rules">
    <li
      v-for="(description, tag) in passwordRulesDescriptions"
      :data-password-rule="tag"
    >
      <span
        class="text-xs text-gray-400"
        :class="{
          hidden: passwordRules[tag as PasswordRuleName](password),
        }"
        >{{ description }}</span
      >
    </li>
  </ul>
  <input type="hidden" name="passwordRulesPassed" :value="passRules" />
</template>

<script setup lang="ts">
  import { zipObject } from 'lodash';
  import { watch } from 'vue';
  import { computed, onMounted, ref } from 'vue';
  import { useDebounceFn } from '@vueuse/core';
  //

  enum PasswordRuleName {
    minLength = 'minLength',
    requireLowercase = 'requireLowercase',
    requireUppercase = 'requireUppercase',
    requireNumbers = 'requireNumbers',
    requireSymbols = 'requireSymbols',
    requirePasswordMatch = 'requirePasswordMatch',
  }

  const passwordRuleNamesArray = Object.entries(PasswordRuleName).map(
    ([_, name]) => name,
  );

  const passwordRulesDescriptions = zipObject(
    [...passwordRuleNamesArray],
    [
      'Password must have at least 8 characters',
      'Password must contain lowercase letters',
      'Password must contain uppercase letters',
      'Password must contain numbers',
      'Password must contain special characters',
      'Password must match password confirmation',
    ],
  );

  const passwordRules: Record<PasswordRuleName, (str: string) => boolean> = {
    minLength: (str: string) => str.length >= 8,
    requireLowercase: (str: string) => /[a-z]/.test(str),
    requireUppercase: (str: string) => /[A-Z]/.test(str),
    requireNumbers: (str: string) => /\d/.test(str),
    requireSymbols: (str: string) => /[\W_]/.test(str),
    requirePasswordMatch: () =>
      !!confirmPassword.value &&
      !!password.value &&
      confirmPassword.value === password.value,
  };

  const password = ref('');
  const confirmPassword = ref('');

  const passRules = computed(() => {
    return !Object.values(passwordRules)
      .map((rule) => rule(password.value))
      .filter((res) => !res).length;
  });

  const emit = defineEmits<{
    (e: 'update', value: boolean): void;
  }>();

  watch(passRules, (val) => {
    emit('update', val);
  });

  onMounted(() => {
    window.setTimeout(() => {
      document.querySelectorAll('[type=password]').forEach((input) =>
        input.addEventListener(
          'keydown',
          useDebounceFn((event) => {
            if (input.getAttribute('name')?.includes('confirm')) {
              confirmPassword.value = event.target.value;
            } else {
              password.value = event.target.value;
            }
          }, 150),
        ),
      );
    }, 0);
  });
</script>
