<template>
  <div class="aspect-square w-full">
    <svg
      class="size-full"
      viewBox="0 0 100 100"
      preserveAspectRatio="xMidYMid meet"
    >
      <!-- Background circle -->
      <circle
        cx="50"
        cy="50"
        r="44"
        stroke-width="12"
        fill="none"
        class="stroke-gray-200"
        transform="rotate(-90 50 50)"
      />
      <!-- Progress circle -->
      <circle
        cx="50"
        cy="50"
        r="44"
        stroke-width="12"
        :stroke-dasharray="circumference"
        :stroke-dashoffset="dashOffset"
        stroke-linecap="round"
        fill="none"
        class="stroke-[#C2AEFD] transition-all duration-300"
        transform="rotate(-90 50 50)"
      />
      <!-- Score text -->
      <text
        x="50"
        y="50"
        text-anchor="middle"
        dominant-baseline="central"
        font-size="32"
        font-weight="bold"
        fill="#374151"
      >
        {{ Math.round(props.score) }}
      </text>
    </svg>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';

  const props = withDefaults(
    defineProps<{
      score: number;
      maxScore?: number;
    }>(),
    {
      maxScore: 100,
    },
  );

  const radius = 44;
  const circumference = computed(() => 2 * Math.PI * radius);

  const dashOffset = computed(() => {
    const progress = Math.max(0, Math.min(1, props.score / props.maxScore));
    return circumference.value * (1 - progress);
  });
</script>
