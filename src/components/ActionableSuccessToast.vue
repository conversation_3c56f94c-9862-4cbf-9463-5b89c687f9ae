<template>
  <p v-if="message">{{ message }}</p>
  <div class="my-4 flex justify-center gap-4">
    <CustomButton
      theme="custom"
      class="btn btn-primary"
      @on-click="confirmToast"
    >
      {{ actions?.confirm ?? 'Confirm' }}
    </CustomButton>
    <CustomButton
      theme="text-like"
      class="!m-0 !p-0 text-primary"
      @on-click="closeToast"
    >
      {{ actions?.dismiss ?? 'Dismiss' }}
    </CustomButton>
  </div>
</template>

<script setup lang="ts">
  import { default as CustomButton } from '@/components/BaseButton.vue';

  type ToastID = string | number;

  defineProps<{
    toastId: ToastID;
    message: string;
    actions?: {
      confirm?: string;
      dismiss?: string;
    };
  }>();
  const emits = defineEmits(['confirm-toast', 'close-toast']);

  const closeToast = () => emits('close-toast');
  const confirmToast = () => {
    emits('confirm-toast');
    closeToast();
  };
</script>
