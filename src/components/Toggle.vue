<template>
  <SwitchGroup as="div" class="flex items-center">
    <Switch
      v-model="value"
      :class="[
        value ? 'bg-primary' : 'bg-gray-400',
        disabled ? 'cursor-not-allowed !bg-gray-200' : 'cursor-pointer',
        'relative inline-flex h-6 w-11 shrink-0 rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2',
      ]"
      :disabled="disabled"
    >
      <span
        aria-hidden="true"
        :class="[
          value ? 'translate-x-5' : 'translate-x-0',
          disabled ? 'bg-gray-100' : 'bg-white',
          'pointer-events-none inline-block size-5 rounded-full shadow ring-0 transition duration-200 ease-in-out',
        ]"
      />
    </Switch>
    <SwitchLabel as="div" class="ml-3 text-sm font-medium text-gray-900">
      <slot name="label" :label="label">
        <span>{{ props.label }}</span>
      </slot>
    </SwitchLabel>
  </SwitchGroup>
</template>
<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { Switch, SwitchGroup, SwitchLabel } from '@headlessui/vue';

  const props = defineProps<{
    modelValue: boolean;
    label: string;
    disabled?: boolean;
    groupClass?: string | string[];
    labelClass?: string | string[];
  }>();

  const value = ref(props.modelValue);

  const emit = defineEmits(['update:modelValue']);

  watch(value, (value, prevValue) => {
    if (value != prevValue) emit('update:modelValue', value);
  });
</script>
