import { DeepNullable } from '@/types/Common';
import { Address } from '@modules/factfind/types/Address';
//

export function createAddress(
  address: Partial<Address>,
): DeepNullable<Address> {
  return {
    addressLineOne: address.addressLineOne ?? null,
    addressLineTwo: address.addressLineTwo ?? null,
    addressLineThree: address.addressLineThree ?? null,
    addressLineFour: address.addressLineFour ?? null,
    city: address.city ?? null,
    countryId: address.countryId ?? null,
    postCode: address.postCode ?? null,
  };
}
