import { useRefData } from '@/stores';
import { Address } from '@modules/factfind/types/Address';
//

export function formatAddressForView(address: Address, glue?: string) {
  const { getCountryById } = useRefData();
  const country = getCountryById(address.countryId as number);
  const addressParts = [
    address.addressLineOne,
    address.addressLineTwo,
    address.addressLineThree,
    address.addressLineFour,
    address.city,
    address.postCode,
    country?.name,
  ].filter(($v) => $v);

  return addressParts.length > 0 ? addressParts.join(glue || ', ') : 'N/A';
}
