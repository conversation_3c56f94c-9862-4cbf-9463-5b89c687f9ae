// @deprecated
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { apiClient } from '../api';

interface MailData {
  subject: string;
  body: string;
  copySelf?: boolean;
  _ctx?: Record<string, unknown>;
}

export interface PostmarkTemplatedMail extends MailData {
  templateAlias?: string;
  templateModel?: Record<string, unknown>;
}

export const queueMessage = async (to: string[], data: PostmarkTemplatedMail) =>
  await apiClient.post(`/api/v1/mailing/queue-message`, {
    to,
    subject: data.subject,
    body: data.body,
    copy_self: data.copySelf ?? false,
    template_alias: data.templateAlias || null,
    template_model: data.templateModel || null,
  });
