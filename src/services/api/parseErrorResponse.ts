import { isArray, isPlainObject, isString } from 'lodash';
import { isJSON } from '@/utils/string/isJSON';

const DEFAULT_ERROR_MESSAGE = 'An error occurred.';

export const parseErrorFromResponse = (
  error: unknown,
  defaultMessage?: string,
): string[] => {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  const _error = (error.response || error) as Record;
  const data = _error.body || {};

  if (isString(data)) {
    if (isJSON(data)) {
      const parsedData = JSON.parse(data);

      if (isPlainObject(parsedData.detail)) {
        return Object.values(parsedData.detail).map(
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          (item) => `${item['loc'].slice(-1)}: ${item['msg']}`,
        );
      }

      if (isArray(parsedData.detail)) {
        return parsedData.detail.map(
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          (item) => `${item['loc'].slice(-1)}: ${item['msg']}`,
        );
      }

      if (isString(parsedData.detail)) {
        return [parsedData.detail || DEFAULT_ERROR_MESSAGE];
      }
    }

    return [data || DEFAULT_ERROR_MESSAGE];
  }

  // If there are no validation errors in the data object then lets try to parse out a message.
  if (data.detail === undefined) {
    return [
      defaultMessage ||
        _error.statusText ||
        _error.message ||
        DEFAULT_ERROR_MESSAGE,
    ];
  }

  return [DEFAULT_ERROR_MESSAGE];
};
