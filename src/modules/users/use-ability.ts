import { Injection<PERSON>ey, Ref, inject, provide, unref } from 'vue';
import { Mon<PERSON><PERSON><PERSON><PERSON>, RawRule, createMongoAbility } from '@casl/ability';
import { Action, Subject, UserAbility } from '@modules/users/types/UserAbility';

export const ABILITY_TOKEN: InjectionKey<Ref<UserAbility>> = Symbol('ability');

export function createUserAbility(
  rules: RawRule<[Action, Subject], MongoQuery>[],
): UserAbility {
  return createMongoAbility<[Action, Subject]>(rules, {
    detectSubjectType: (object) => object,
  });
}

export function useUserAbility(): UserAbility {
  const ability = unref(inject(ABILITY_TOKEN));

  if (!ability) {
    throw new Error(
      'Cannot inject Ability instance because it was not provided',
    );
  }

  return ability;
}

export function provideUserAbility(ability: Ref<UserAbility>) {
  provide(ABILITY_TOKEN, ability);
}
