import { MongoQuery, RawRule } from '@casl/ability';
import { UserRole } from '@modules/auth';
import { Action, Subject } from './UserAbility';

export type UserId = number;

export interface User {
  id: UserId;
  email: string;
  firstName: string;
  lastName: string;
  type: string;
  permissions: RawRule<[Action, Subject], MongoQuery>[];
  groups?: UserRole[];
}

export interface UserDTO {
  id: UserId;
  email: string;
  first_name: string;
  last_name: string;
  type: string;
  permissions: RawRule<[Action, Subject], MongoQuery>[];
}
