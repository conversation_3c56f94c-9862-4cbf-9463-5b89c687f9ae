export interface IExpectedFeeType {
  toString: () => string;
  toNumber: () => number;
}

export enum ExpectedFeeTypeEnum {
  FeeFromProvider = 1,
  FeeFromClient = 2,
  CommissionFromProvider = 3,
}

export class ExpectedFeeType implements IExpectedFeeType {
  constructor(private value: number) {}

  toString(): string {
    const map = new Map<number, string>([
      [ExpectedFeeTypeEnum.FeeFromProvider, 'Fee From Provider'],
      [ExpectedFeeTypeEnum.FeeFromClient, 'Fee From Client'],
      [ExpectedFeeTypeEnum.CommissionFromProvider, 'Commission From Provider'],
    ]);

    return map.get(this.value) ?? '';
  }

  toNumber(): number {
    return this.value;
  }
}
