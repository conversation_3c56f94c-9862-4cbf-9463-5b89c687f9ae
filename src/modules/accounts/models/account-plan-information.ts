import { Nullable } from '@/types/Common';
import {
  AllFormValues,
  isInvestment,
  isPension,
  isPensionIncomePaying,
  isProtection,
} from '@modules/accounts/models/form-model';

interface ChargesFigures {
  chargesPlanAMC: Nullable<string>;
  chargesFundAMC: Nullable<string>;
  chargesDFMfee: Nullable<string>;
  additionalfeePAAmount: Nullable<string>;
  annualOverallChargePAPercentageFigures: Nullable<string>;
}

interface ChargesProjections {
  commencementDate: Nullable<string>;
  retirementDate: Nullable<string>;
  dateOfProjection: Nullable<string>;
  retirementDateOnProjection: Nullable<string>;
  rateOfProjection: Nullable<string>;
  projectionFigure: Nullable<string>;
  annualOverallChargePAPercentageProjections: Nullable<string>;
}

interface SafeguardingBenefits {
  guaranteedAnnuityRateGAR: Nullable<boolean>;
  guaranteedMinPensionGMP: Nullable<boolean>;
  isTFCGreaterThan25Percentage: Nullable<boolean>;
  anyLoyaltyBonus: Nullable<boolean>;
  protectedRetirementAge: Nullable<boolean>;
}

interface OtherPensionInfo {
  UFPLSAvailable: Nullable<boolean>;
  dischargeFormsOnFile: Nullable<boolean>;
  drawdownAvailable: Nullable<boolean>;
  nomineeFlexiAccessDrawdownAvailable: Nullable<boolean>;
  withProfits: Nullable<boolean>;
  switchingStrategyOnFile: Nullable<boolean>;
  lifeCover: Nullable<boolean>;
  contributingHistoryOnFile: Nullable<boolean>;
  deathBenefitsOnFile: Nullable<boolean>;
  availableFundsForSwitchOnFile: Nullable<boolean>;
  adviserRemunerationAllowable: Nullable<boolean>;
}

interface OtherInvestmentInfo {
  withProfits: Nullable<boolean>;
  contributingHistoryOnFile: Nullable<boolean>;
  availableFundsForSwitchOnFile: Nullable<boolean>;
  adviserRemunerationAllowable: Nullable<boolean>;
  withdrawalScheduleOnFile: Nullable<boolean>;
  transactionCGTReportOnFile: Nullable<boolean>;
  wrapperStructureDetailsOnFile: Nullable<boolean>;
  segmentationStructureHistoryOnFile: Nullable<boolean>;
}

interface OtherPensionIncomePayingInfo {
  deathBenefitsOnFile: Nullable<boolean>;
  detailsOfSchemeOnFile: Nullable<boolean>;
  detailsOfPensionableServiceOnFile: Nullable<boolean>;
}

interface OtherProtectionInfo {
  adviserRemunerationAllowable: Nullable<boolean>;
  insuranceTypeDetailsOnFile: Nullable<boolean>;
  detailsOfTrustOnFile: Nullable<boolean>;
  insuranceCostDetailsOnFile: Nullable<boolean>;
  detailsOfPersonInsuredOnFile: Nullable<boolean>;
  insuranceHistoryDetailsOnFile: Nullable<boolean>;
  latestPolicyStatementOnFile: Nullable<boolean>;
}

export interface Constituent {
  id: number | null;
  ISIN: string;
  fund: string;
  fundCharge: number;
  investedPercentage: number;
  value: number;
  weightedCharge: number;
}

export interface AccountPlanInformation {
  id?: number | string;
  dateOfInformation: string;
  notes: string;
  portfolioModel: Nullable<number>;
  fundValue: Nullable<string>;
  transferValue: Nullable<string>;
  chargesFigures: ChargesFigures;
  chargesProjections: ChargesProjections;
  // Other details
  safeguardingBenefits: SafeguardingBenefits;
  otherPensionInfo: OtherPensionInfo;
  otherInvestmentInfo: OtherInvestmentInfo;
  otherPensionIncomeInformation: OtherPensionIncomePayingInfo;
  otherProtectionInfo: OtherProtectionInfo;
  constituents: Array<Constituent>;
}

const mapConstituentsToDto = (constitients: Array<Constituent>) => {
  return constitients.map((constituent) => {
    return {
      id: constituent.id,
      isin: constituent.ISIN,
      fund: constituent.fund,
      fund_charge: constituent.fundCharge,
      invested_percentage: constituent.investedPercentage,
      value: constituent.value,
      weighted_charge: constituent.weightedCharge,
    };
  });
};

export const strToPercentageDTO = (value: string | null) =>
  value ? String(Number(value)) : null;
export const percentageDTOtoStr = (value: number | null | undefined) =>
  value !== null && value !== undefined ? value.toFixed(2).toString() : null;
export const mapToDto = (
  accountPlanInformation: AllFormValues,
  product_layout: string | null,
) => {
  const plan_information = {
    date_of_information: accountPlanInformation.dateOfInformation,
    data_layout: product_layout,
    notes: accountPlanInformation.notes,
  };

  if (
    isInvestment(accountPlanInformation) ||
    isPension(accountPlanInformation)
  ) {
    plan_information['fund_value'] = accountPlanInformation.fundValue || null;
    plan_information['transfer_value'] =
      accountPlanInformation.transferValue || null;
    plan_information['portfolio_model'] = accountPlanInformation.portfolioModel;
    plan_information['charges_figures'] = {
      charges_plan_amc: strToPercentageDTO(
        accountPlanInformation.chargesFigures?.chargesPlanAMC || null,
      ),
      charges_fund_amc: strToPercentageDTO(
        accountPlanInformation.chargesFigures?.chargesFundAMC || null,
      ),
      charges_dfm_fee: strToPercentageDTO(
        accountPlanInformation.chargesFigures?.chargesDFMfee || null,
      ),
      any_additional_pounds_fee_pa:
        accountPlanInformation.chargesFigures.additionalfeePAAmount || null,
      annual_overall_charge_pa_percentage_figures: strToPercentageDTO(
        accountPlanInformation.chargesFigures
          ?.annualOverallChargePAPercentageFigures,
      ),
    };
    plan_information['constituents'] = mapConstituentsToDto(
      accountPlanInformation.constituents ?? [],
    );
  }

  if (isPension(accountPlanInformation)) {
    plan_information['charges_projections'] = {
      account_commencement_date:
        accountPlanInformation.chargesProjections.commencementDate || null,
      retirement_date:
        accountPlanInformation.chargesProjections.retirementDate || null,
      date_of_projection:
        accountPlanInformation.chargesProjections.dateOfProjection || null,
      retirement_date_on_projections:
        accountPlanInformation.chargesProjections.retirementDateOnProjection ||
        null,
      rate_of_projection: strToPercentageDTO(
        accountPlanInformation.chargesProjections.rateOfProjection,
      ),
      projection_figure_pounds:
        accountPlanInformation.chargesProjections.projectionFigure || null,
      projections_overall_charge_pa:
        accountPlanInformation.chargesProjections
          .annualOverallChargePAPercentageProjections,
    };
    plan_information['safeguarding_benefits'] = {
      guaranteed_annuity_rate_gar:
        accountPlanInformation.safeguardingBenefits?.guaranteedAnnuityRateGAR,
      guaranteed_min_pension_gmp:
        accountPlanInformation.safeguardingBenefits?.guaranteedMinPensionGMP,
      is_tfc_greater_than_25_percent:
        accountPlanInformation.safeguardingBenefits
          ?.isTFCGreaterThan25Percentage,
      any_loyalty_bonus:
        accountPlanInformation.safeguardingBenefits?.anyLoyaltyBonus,
      protected_retirement_age:
        accountPlanInformation.safeguardingBenefits?.protectedRetirementAge,
    };
    plan_information['pension_info'] = {
      adviser_remuneration_allowable:
        accountPlanInformation.otherPensionInfo?.adviserRemunerationAllowable,
      available_funds_for_switch_on_file:
        accountPlanInformation.otherPensionInfo?.availableFundsForSwitchOnFile,
      contributing_history_on_file:
        accountPlanInformation.otherPensionInfo?.contributingHistoryOnFile,
      death_benefits_on_file:
        accountPlanInformation.otherPensionInfo?.deathBenefitsOnFile,
      discharge_forms_on_file:
        accountPlanInformation.otherPensionInfo?.dischargeFormsOnFile,
      drawdown_available:
        accountPlanInformation.otherPensionInfo?.drawdownAvailable,
      life_cover: accountPlanInformation.otherPensionInfo?.lifeCover,
      nominee_flexi_access_drawdown_available:
        accountPlanInformation.otherPensionInfo
          ?.nomineeFlexiAccessDrawdownAvailable,
      switching_strategy_on_file:
        accountPlanInformation.otherPensionInfo?.switchingStrategyOnFile,
      ufpls_available: accountPlanInformation.otherPensionInfo?.UFPLSAvailable,
      with_profits: accountPlanInformation.otherPensionInfo?.withProfits,
    };
  }

  if (isInvestment(accountPlanInformation)) {
    plan_information['investment_info'] = {
      with_profits: accountPlanInformation.otherInvestmentInfo?.withProfits,
      contributing_history_on_file:
        accountPlanInformation.otherInvestmentInfo?.contributingHistoryOnFile,
      available_funds_for_switch_on_file:
        accountPlanInformation.otherInvestmentInfo
          ?.availableFundsForSwitchOnFile,
      adviser_remuneration_allowable:
        accountPlanInformation.otherInvestmentInfo
          ?.adviserRemunerationAllowable,
      withdrawal_schedule_on_file:
        accountPlanInformation.otherInvestmentInfo?.withdrawalScheduleOnFile,
      transaction_cgt_report_on_file:
        accountPlanInformation.otherInvestmentInfo?.transactionCGTReportOnFile,
      wrapper_structure_details_on_file:
        accountPlanInformation.otherInvestmentInfo
          ?.wrapperStructureDetailsOnFile,
      segmentation_structure_history_on_file:
        accountPlanInformation.otherInvestmentInfo
          ?.segmentationStructureHistoryOnFile,
    };
  }
  if (isPensionIncomePaying(accountPlanInformation)) {
    plan_information['pension_income_paying_info'] = {
      death_benefits_on_file:
        accountPlanInformation.otherPensionIncomePaying?.deathBenefitsOnFile,
      details_of_scheme_on_file:
        accountPlanInformation.otherPensionIncomePaying?.detailsOfSchemeOnFile,
      details_of_pensionable_service_on_file:
        accountPlanInformation.otherPensionIncomePaying
          ?.detailsOfPensionableServiceOnFile,
    };
  }

  if (isProtection(accountPlanInformation)) {
    plan_information['protection_info'] = {
      adviser_remuneration_allowable:
        accountPlanInformation.otherProtectionInfo
          ?.adviserRemunerationAllowable,
      insurance_type_details_on_file:
        accountPlanInformation.otherProtectionInfo?.insuranceTypeDetailsOnFile,
      details_of_trust_on_file:
        accountPlanInformation.otherProtectionInfo?.detailsOfTrustOnFile,
      insurance_cost_details_on_file:
        accountPlanInformation.otherProtectionInfo?.insuranceCostDetailsOnFile,
      details_of_person_insured_on_file:
        accountPlanInformation.otherProtectionInfo
          ?.detailsOfPersonInsuredOnFile,
      insurance_history_details_on_file:
        accountPlanInformation.otherProtectionInfo
          ?.insuranceHistoryDetailsOnFile,
      latest_policy_statement_on_file:
        accountPlanInformation.otherProtectionInfo?.latestPolicyStatementOnFile,
    };
  }

  return plan_information;
};
