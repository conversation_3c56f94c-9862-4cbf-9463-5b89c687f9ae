import { SelectOption } from '@/components/form/fields/field-model';

export enum FeeModelEnum {
  'Tiered' = 'Tiered Structure',
  'Percent100' = '1%',
  'Percent075' = '0.75%',
  'Percent050' = '0.5%',
  'PercentNil' = '0%',
  'Invoice' = 'Invoice',
  'Custom' = 'Custom',
  'Percent150' = '1.5%',
  'Percent025' = '0.25%',
}

export const toSelectOptions = (): SelectOption[] =>
  Object.entries(FeeModelEnum).map(
    (value: Array<string | FeeModelEnum>, index: number) => ({
      label: value[1],
      value: ++index,
    }),
  );
