import { AdviceFrequencyEnum } from '@modules/factfind/models/frequency';

export interface Advice {
  id: number | undefined;
  type: number;
  description: string;
  isImplemented: boolean;
  isAccepted: boolean;
  amount?: number;
  frequency?: AdviceFrequencyEnum;
  portfolioId?: number;
  accountId?: number;
}

export type AdviceDTO = {
  id: number | undefined;
  advice_type_id: number;
  note: string;
  is_implemented: boolean;
  is_accepted: boolean;
  amount?: number;
  frequency?: AdviceFrequencyEnum;
  portfolio_id?: number;
  referenced_account_id?: number;
};
