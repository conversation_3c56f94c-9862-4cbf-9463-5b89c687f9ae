import { InferType, array, boolean, number, object, string } from 'yup';
import { apiClient } from '@/services/api';
import { DateTime } from '@/utils/dateTime';
import { ClientId } from '@modules/clients';
import { AccountId } from '../types';
import { SingleAccount } from '../models';

type SupportedSections = 'has_plan_information' | 'has_investment_mix';
type ProductLayouts =
  | 'pension_layout'
  | 'protection_layout'
  | 'investment_layout'
  | 'pension_income_paying_layout';

const getAccountSchema = object({
  id: number().required(),
  acc_no: string().nullable(),
  sub_acc_no: string().nullable(),
  adviser: object({
    id: number().required(),
    first_name: string().required(),
    last_name: string().nullable().defined(),
  })
    .defined()
    .nullable(),
  created: string().nullable(),
  clients: array()
    .of(
      object({
        id: number().required(),
        first_name: string().required(),
        last_name: string().nullable().defined(),
      }),
    )
    .required(),
  fee_split_template: object({
    id: number().required(),
    name: string().required(),
    is_active: boolean().required(),
  }).nullable(),
  fee_model: number().nullable(),
  portfolio_model_id: number().nullable().defined(),
  status: object({
    id: number().required(),
    name: string().required(),
  }).required(),
  provider_id: number().nullable().defined(),
  type_id: number().required(),
  additional_info: string().defined(),
  supported_sections: array()
    .of(
      string()
        .oneOf<SupportedSections>([
          'has_plan_information',
          'has_investment_mix',
        ])
        .defined(),
    )
    .required(),
  product_layout: string()
    .oneOf<ProductLayouts>([
      'pension_layout',
      'protection_layout',
      'investment_layout',
      'pension_income_paying_layout',
    ])
    .defined()
    .nullable(),
});

type AccountDTO = InferType<typeof getAccountSchema>;

export default async (accountId: AccountId): Promise<SingleAccount> => {
  const response = await apiClient.get<Promise<unknown>>(
    `/api/v1/holdings/${accountId}`,
  );

  const accountDTO: AccountDTO = await getAccountSchema.validate(response);

  return {
    id: accountDTO.id,
    providerId: accountDTO.provider_id,
    advisor: accountDTO.adviser
      ? {
          id: accountDTO.adviser.id,
          firstName: accountDTO.adviser.first_name,
          lastName: accountDTO.adviser.last_name,
        }
      : null,
    typeId: accountDTO.type_id,
    feeSplitTemplate: accountDTO.fee_split_template
      ? {
          id: accountDTO.fee_split_template.id,
          name: accountDTO.fee_split_template.name,
          isActive: accountDTO.fee_split_template.is_active,
        }
      : null,
    feeModel: accountDTO.fee_model ?? null,
    portfolioModelId: accountDTO.portfolio_model_id,
    clients: accountDTO.clients.map((client) => ({
      id: client.id as ClientId,
      firstName: client.first_name,
      lastName: client.last_name ?? null,
    })),
    accountNumber: accountDTO.acc_no || '',
    subAccountNumber: accountDTO.sub_acc_no || '',
    created: new DateTime(accountDTO.created),
    status: accountDTO.status,
    additionalInfo: accountDTO.additional_info || '',
    hasPlanInformation: accountDTO.supported_sections.includes(
      'has_plan_information',
    ),
    hasInvestmentMix:
      accountDTO.supported_sections.includes('has_investment_mix'),
    productLayout: accountDTO.product_layout,
  };
};
