import { apiClient } from '@/services/api';

interface Payload {
  retirement_date_on_projections: string;
  date_of_projection: string;
  projection_figure_pounds: string;
  fund_value: string;
  rate_of_projection: string;
}

export default async (payload: Payload) => {
  const response = await apiClient.post<typeof payload, Promise<string>>(
    `/api/v2/holdings/plan_information/calculate_annual_overall_charge_using_projections`,
    payload,
  );

  return Number(response).toFixed(2);
};
