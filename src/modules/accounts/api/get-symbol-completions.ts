import { apiClient } from '@/services/api';

export interface SymbolDetails {
  code: string;
  name: string;
}

export const getSymbolCodeCompletions = async (
  code: string,
  limit: number,
): Promise<SymbolDetails[]> => {
  const response = await apiClient.get<Promise<SymbolDetails[]>>(
    `/api/v2/refdata/symbols/code-completions?code=${code}&limit=${limit}`,
  );
  return response;
};

export const getSymbolNameCompletions = async (
  name: string,
  limit: number,
): Promise<SymbolDetails[]> => {
  const response = await apiClient.get<Promise<SymbolDetails[]>>(
    `/api/v2/refdata/symbols/name-completions?name=${name}&limit=${limit}`,
  );
  return response;
};
