import { apiClient } from '@/services/api';
import { CaseId, TaskCustomCommand } from '@modules/cases';
import { TaskId } from '@modules/tasks';

type Command = TaskCustomCommand.AddProposedAccount;

type ProposedAccount = {
  provider_id: number;
  account_type_id: number;
  advice_lines: Array<{
    advice_type_id: number;
    note: string;
  }>;
  client_ids: Array<number>;
};

type Body = ProposedAccount;

export default async (
  caseId: CaseId,
  taskId: TaskId,
  account: ProposedAccount,
) => {
  await apiClient.patch<{ command: Command; payload: Body }, Promise<void>>(
    `/api/v2/case/${caseId}/advice_selection/${taskId}`,
    {
      command: TaskCustomCommand.AddProposedAccount,
      payload: { ...account },
    },
  );
};
