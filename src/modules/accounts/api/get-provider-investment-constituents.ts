import { apiClient } from '@/services/api';
import { AccountId } from '../types';
import { ProviderInvestmentConstituents } from '../models';

interface apiConstituent {
  isin: string;
  fund: string;
  valuation: number;
  fund_charge_percent: string | null;
}

export default async (
  accountId: AccountId,
  queryDate: string,
): Promise<ProviderInvestmentConstituents[]> => {
  const response = await apiClient.get<Promise<apiConstituent[]>>(
    `/api/v1/holdings/${accountId}/investment-constituents?query_date=${queryDate}`,
  );
  return response.map((constituent) => ({
    ISIN: constituent.isin,
    fund: constituent.fund,
    value: constituent.valuation,
    fundChargePercent: constituent.fund_charge_percent,
  }));
};
