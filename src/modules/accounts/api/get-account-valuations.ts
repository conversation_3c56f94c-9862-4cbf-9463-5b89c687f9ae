import { boolean } from 'yup';
import { array, number, object, string } from 'yup';
import { apiClient } from '@/services/api';
import { DateTime } from '@/utils/dateTime';
import { Money } from '@/utils/money';
import { ValuationType } from '@modules/factfind/types';
import { AccountId } from '../types';

const getAccountValuationsSchema = array()
  .of(
    object({
      date: string().required(),
      is_actual: boolean().required(),
      amount: string().required(),
    }).required(),
  )
  .required();

export default async (accountId: AccountId) => {
  const response = await apiClient.get<Promise<unknown>>(
    `/api/v1/holdings/${accountId}/valuation`,
  );

  const accountValuationsDTO =
    await getAccountValuationsSchema.validate(response);

  return accountValuationsDTO.map((valuation) => ({
    type: (valuation.is_actual ? 'actual' : 'estimate') as ValuationType,
    amount: new Money(Number(valuation.amount)),
    date: new DateTime(valuation.date),
  }));
};
