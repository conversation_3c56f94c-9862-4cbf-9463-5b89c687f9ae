import { array, number, object, string } from 'yup';
import { apiClient } from '@/services/api';
import { DateTime, IDateTime } from '@/utils/dateTime';
import { IMoney, Money } from '@/utils/money';
import { AccountId } from '../types';

export interface AccountFeeStatementLine {
  id: number;
  paidDate: IDateTime;
  statementId: number;
  gross: IMoney;
  feeType: string;
}

const getAccountFeesSchema = object({
  fees: array()
    .of(
      object({
        id: number().required(),
        statement_id: number().required(),
        paid_date: string().nullable().defined(),
        type: string().required(),
        amount: string().required(),
      }).required(),
    )
    .required(),
});

export default async (accountId: AccountId) => {
  const response = await apiClient.get<Promise<unknown>>(
    `/api/v1/holdings/${accountId}/fees`,
  );

  const accountFeesDTO = await getAccountFeesSchema.validate(response);

  return accountFeesDTO.fees.map((fee) => ({
    id: fee.id,
    statementId: fee.statement_id,
    feeType: fee.type,
    gross: new Money(Number(fee.amount)),
    paidDate: new DateTime(fee.paid_date),
  }));
};
