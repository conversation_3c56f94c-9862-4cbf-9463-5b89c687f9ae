import { apiClient } from '@/services/api';

interface Payload {
  charges_plan_amc: string;
  charges_fund_amc: string;
  charges_dfm_fee: string;
  additional_fee_amount: string;
  fund_value: string;
}

export default async (payload: Payload) => {
  const response = await apiClient.post<typeof payload, Promise<number>>(
    `/api/v2/holdings/plan_information/calculate_annual_overall_charge_using_figures`,
    payload,
  );

  return Number(response).toFixed(2);
};
