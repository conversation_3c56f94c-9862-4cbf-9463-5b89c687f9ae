import { DeviceFormFactors, DevicePlatform } from 'ably';

export class PushNotificationsDeviceRegistrationError extends Error {
  constructor(message: string) {
    super(`Push notifications device registration failed: ${message}`);

    this.constructor = PushNotificationsDeviceRegistrationError;
    Object.setPrototypeOf(
      this,
      PushNotificationsDeviceRegistrationError.prototype,
    );
  }
}

type IOSPushRecipient = {
  platform: 'ios';
  transport_type: 'apns';
  device_token: string;
};

type AndroidPushRecipient = {
  platform: 'android';
  transport_type: 'fcm';
  registration_token: string;
};

export type RegistrationRequest = {
  id: string;
  client_id: string;
  platform: DevicePlatform;
  form_factor: DeviceFormFactors.PHONE;
  push_recipient: IOSPushRecipient | AndroidPushRecipient;
};

export type RegistrationResponse = {
  id: string;
  clientId: string;
  formFactor: string;
  platform: string;
  push: {
    state: string;
    recipient:
      | {
          transportType: string;
          deviceToken: string;
          apnsDeviceTokens: {
            default: string;
          };
        }
      | {
          transportType: string;
          registrationToken: string;
        };
  };
  appId: string;
  deviceIdentityToken: {
    token: string;
    keyName: string;
    issued: number;
    expires: number;
    capability: string;
    clientId: string;
  };
  modified: number;
};

export type TransportType = 'apns' | 'fcm';
