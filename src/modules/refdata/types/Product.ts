export type ProductType =
  | 'account'
  | 'property'
  | 'company_shares'
  | 'crypto_currency'
  | 'other_asset'
  | 'term_policy'
  | 'indemnity_policy'
  | 'whole_of_life_policy'
  | 'income_protection_policy'
  | 'mortgage'
  | 'personal_loan'
  | 'credit_card'
  | 'other_debt'
  | 'defined_benefit_pension';

export interface Product {
  id: number;
  name: string;
  productTypeGroupId: number;
  type: ProductType;
}
