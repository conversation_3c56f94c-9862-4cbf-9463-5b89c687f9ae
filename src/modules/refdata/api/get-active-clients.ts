import { apiClient } from '@/services/api';
import { ActiveClient } from '@modules/refdata';
import { ClientId } from '@modules/clients';

type Response = {
  id: number;
  first_name: string;
  last_name: string;
}[];

export const getActiveClients = async (): Promise<ActiveClient[]> => {
  const response = await apiClient.get<Promise<Response>>(
    `/api/v1/clients/active-clients`,
  );

  return response.map((item) => ({
    id: item.id as ClientId,
    firstName: item.first_name,
    lastName: item.last_name,
  }));
};
