import { Ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useStore } from './store';
import { FeeSplitTemplate } from './fee-split-template';

interface UseFeeSplitTemplatesApi {
  feeSplitTemplates: Ref<FeeSplitTemplate[]>;
  fetchForAdvisor: (advisorId: number) => Promise<void>;
}

export default function (): UseFeeSplitTemplatesApi {
  const { fetchAdvisorItems } = useStore();
  const { list } = storeToRefs(useStore());

  return {
    feeSplitTemplates: list,
    fetchForAdvisor: fetchAdvisorItems,
  };
}
