import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import { Product } from '@modules/refdata/types/Product';
import { useRefData } from '@/stores';

export const useProductStore = defineStore('product', () => {
  const { getProductTypes } = useRefData();

  const products = ref<Product[]>(
    (getProductTypes || []).map((item) => ({
      ...item,
      productTypeGroupId: item.product_type_group_id,
    })),
  );

  const getProducts = computed<Product[]>(() => products.value);

  const getProductsByGroupId = computed(function () {
    return (groupId: number): Product[] => {
      return this.products.filter(
        (item: Product) => item.productTypeGroupId == groupId,
      );
    };
  });

  const getProductById = computed(function () {
    return (productId: Product['id']): Product | undefined => {
      return products.value.find((item: Product) => item.id == productId);
    };
  });

  const getProductsByType = computed(() => {
    return function (type: Product['type']): Product[] {
      return products.value.filter((product) => product.type === type);
    };
  });

  return {
    products,
    getProducts,
    getProductsByGroupId,
    getProductById,
    getProductsByType,
  };
});
