import { defineStore } from 'pinia';

import { Nullable } from '@/types/Common';
import { useRefData } from '@/stores';

import {
  CashFlow,
  Expenditure,
  Income,
  isExpenditureFlow,
  isIncomeFlow,
} from './cashflow';
//

export type Cashflow = {
  id: number;
  income_expenditure_group: {
    id: number;
    type: 'Income' | 'Expenditure';
    name: string;
    description: string;
  };
  name: string;
  is_active: boolean;
  is_essential: Nullable<boolean>;
};

export type CashflowStore = {
  incomes: Income[];
  expenditures: Expenditure[];
  isLoaded: boolean;
};

export type Getters = {
  expenditureGroups: (state: CashflowStore) => Expenditure[];
  incomeGroups: (state: CashflowStore) => Income[];
  getExpendituresByGroupId: (
    state: CashflowStore,
  ) => (groupId: number) => Expenditure[];
  getIncomesByGroupId: (state: CashflowStore) => (groupId: number) => Income[];
  incomeByTypeId: (
    state: CashflowStore,
  ) => (typeId: number) => Income | undefined;
  expenditureByTypeId: (
    state: CashflowStore,
  ) => (id: number) => Expenditure | undefined;
};

export type Actions = {
  setCashflows: () => Promise<void>;
};

const haveLoadedItems = (state: CashflowStore): boolean => {
  return (
    state.isLoaded && state.expenditures.length > 0 && state.incomes.length > 0
  );
};

const filterDuplications = <TFlowType extends CashFlow>(items: TFlowType[]) => {
  return [
    ...new Map(
      items.map((item) => [item.income_expenditure_group_id, item]),
    ).values(),
  ];
};

export const useCashflowStore = defineStore<
  'cashflow-store',
  CashflowStore,
  Getters,
  Actions
>('cashflow-store', {
  state: () => ({
    incomes: [],
    expenditures: [],
    isLoaded: false,
  }),
  getters: {
    expenditureGroups(state: CashflowStore): Expenditure[] {
      return filterDuplications<Expenditure>(state.expenditures);
    },

    incomeGroups(): Income[] {
      return filterDuplications<Income>(this.incomes);
    },

    getIncomesByGroupId:
      (state: CashflowStore) =>
      (groupId: number): Income[] => {
        return state.incomes.filter(
          (income) => income.income_expenditure_group_id === groupId,
        );
      },

    getExpendituresByGroupId:
      (state: CashflowStore) =>
      (groupId: number): Expenditure[] => {
        return state.expenditures.filter(
          (income) => income.income_expenditure_group_id === groupId,
        );
      },
    incomeByTypeId:
      (state: CashflowStore) =>
      (typeId: number): Income | undefined => {
        return state.incomes.find((income) => income.id === typeId);
      },

    expenditureByTypeId:
      (state: CashflowStore) =>
      (id: number): Expenditure | undefined => {
        return state.expenditures.find((expenditure) => expenditure.id === id);
      },
  },
  actions: {
    async setCashflows() {
      if (haveLoadedItems(this)) return;

      const { getCashflows } = useRefData();

      const cashflows = getCashflows.map((dto) => ({
        id: dto.id,
        group_name: dto.income_expenditure_group.name,
        flow_type: dto.income_expenditure_group.type,
        income_expenditure_group_id: dto.income_expenditure_group.id,
        name: dto.name,
        is_essential: dto.is_essential,
      }));

      this.incomes = cashflows.filter(isIncomeFlow);
      this.expenditures = cashflows.filter(isExpenditureFlow);
      this.isLoaded = true;
    },
  },
});
