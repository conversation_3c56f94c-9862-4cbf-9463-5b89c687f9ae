import { Nullable } from '@/types/Common';
import { Advisor<PERSON><PERSON><PERSON><PERSON>, AdvisorStatus } from '../types/Advisor';

export interface AdvisorDTO {
  id: number;
  first_name: string;
  last_name: string;
  roles: Advisor<PERSON><PERSON><PERSON><PERSON>[];
  email: string;
  status: Advisor<PERSON>tatus;
  reached_competent_adviser_status: boolean;
  reached_competent_adviser_date: Nullable<string>;
}

export interface CreateAdvisorDTO {
  first_name: string;
  last_name: string;
  roles: Advisor<PERSON><PERSON><PERSON><PERSON>[];
  email: string;
  status: AdvisorStat<PERSON>;
  reached_competent_adviser_status: boolean;
  reached_competent_adviser_date: Nullable<string>;
}

export interface UpdateAdvisorDTO {
  first_name: string;
  last_name: string;
  roles: Advisor<PERSON><PERSON><PERSON><PERSON>[];
  status: AdvisorStatus;
  reached_competent_adviser_status: boolean;
  reached_competent_adviser_date: Nullable<string>;
}
