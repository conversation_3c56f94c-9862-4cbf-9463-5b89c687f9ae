import { Paginable } from '@/types/Pagination';
import { Nullable } from '@/types/Common';
import { IDateTime } from '@/utils/dateTime';
import { Mailable } from '@/types/Mailable';
import { Person } from '@/types/Person';

export type AdvisorId = number;

export type AdvisorStatus = 'Active' | 'Inactive';

export enum AdvisorPaginationEnum {
  NO_PAGINATION = 'no_pagination',
}

export enum AdvisorStatusEnum {
  ACTIVE = 'Active',
  INACTIVE = 'Inactive',
}

export enum AdvisorRoleEnum {
  Client = 'client',
  SuperAdmin = 'superadmin',
  Adviser = 'adviser',
  Compliance = 'compliance',
  Introducer = 'introducer',
  Paraplanner = 'paraplanner',
  RelationshipManager = 'relationship_manager',
  CaseManagement = 'case_management',
}

export interface Advisor extends Person, Mailable {
  id: AdvisorId;
  email: string;
  roles: AdvisorRoleEnum[];
  status: AdvisorStatus;
  reachedCompetentAdviserStatus: boolean;
  reachedCompetentAdviserStatusDate: Nullable<IDateTime>;
}

export interface AdvisorList extends Paginable<Advisor> {}
