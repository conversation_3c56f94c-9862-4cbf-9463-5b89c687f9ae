import { defineStore } from 'pinia';
import { useRefData } from '@/stores';
import { Advisor, AdvisorStatusEnum } from '../types/Advisor';
import { getAdvisors } from '../api';

export type AdvisorsGroups = Array<{
  value: string;
  label: string;
}>;

interface State {
  advisors: Advisor[];
  activeAdvisors: Advisor[];
  advisorsRoles: AdvisorsGroups;
  status: {
    pending: boolean;
    error: Error | null;
  };
}

type fetchActiveAdvisorsOptions = { force: boolean };

type Getters = {
  getAdvisors: (state: State) => Advisor[];
  getActiveAdvisors: (state: State) => Advisor[];
  getAdvisorById(state: State): (id: Advisor['id']) => Advisor | null;
  isBusy(state: State): boolean;
  getAdvisorsRoles: (state: State) => AdvisorsGroups;
};

interface Actions {
  fetchAdvisors(): Promise<void>;
  fetchActiveAdvisors(options?: fetchActiveAdvisorsOptions): Promise<void>;
}

export const useAdvisorsStore = defineStore<
  'advisors',
  State,
  Getters,
  Actions
>('advisors', {
  state: () => ({
    advisors: [],
    advisorsRoles: [],
    activeAdvisors: [],
    status: {
      error: null,
      pending: false,
    },
  }),
  getters: {
    getAdvisors(state: State) {
      return state.advisors;
    },
    getActiveAdvisors(state: State) {
      return state.activeAdvisors;
    },
    getAdvisorById(state: State) {
      return (id: Advisor['id']): Advisor | null => {
        return (
          state.advisors.find((advisor: Advisor) => advisor.id === id) || null
        );
      };
    },
    getAdvisorsRoles(state: State) {
      const { getAdvisorsRoles } = useRefData();
      return getAdvisorsRoles;
    },
    isBusy(state: State) {
      return state.status.pending;
    },
  },
  actions: {
    async fetchAdvisors() {
      if (this.advisors.length) {
        return;
      }

      try {
        this.status.pending = true;

        const { items } = await getAdvisors();

        this.advisors = items;

        this.status.pending = false;
      } catch (e: unknown) {
        if (e instanceof Error) {
          this.status.error = e;
        } else {
          this.status.error = new Error(
            'Something went wrong while fetching advisors.',
          );
        }
      }
    },
    async fetchActiveAdvisors(options = { force: false }) {
      const { force } = options;
      if (this.activeAdvisors.length && !force) {
        return;
      }

      try {
        this.status.pending = true;

        const { items } = await getAdvisors({
          status: AdvisorStatusEnum.ACTIVE,
        });

        this.activeAdvisors = items;
        this.status.pending = false;
      } catch (e: unknown) {
        if (e instanceof Error) {
          this.status.error = e;
        } else {
          this.status.error = new Error(
            'Something went wrong while fetching advisors.',
          );
        }
      }
    },
  },
});
