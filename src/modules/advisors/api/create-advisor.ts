import { apiClient } from '@/services/api';
import { AdvisorDTO, CreateAdvisorDTO } from '../dtos';

export class UniqueEmailError extends Error {
  constructor(message: string) {
    super(message);
  }
}

type TUniqueEmailError = {
  response: {
    data: {
      detail: string;
    };
  };
};

const isUniqueEmailError = (e: any): e is TUniqueEmailError => {
  return 'detail' in e.response.data;
};

export default async (body: CreateAdvisorDTO) => {
  await apiClient
    .post<CreateAdvisorDTO, Promise<AdvisorDTO>>(`/api/v1/advisor`, body)
    .catch((e) => {
      if (isUniqueEmailError(e)) {
        throw new UniqueEmailError(e.response.data.detail);
      }
      throw e;
    });
};
