import { DateTime } from '@/utils/dateTime';
import { QueryParams, apiClient } from '@/services/api';
import { AdvisorList } from '../types/Advisor';
import { AdvisorDTO } from '../dtos';

interface GetAdvisorsDTO {
  administrators: AdvisorDTO[];
  total_count: number;
}

export default async (queryParams?: QueryParams): Promise<AdvisorList> => {
  const response = await apiClient.get<Promise<GetAdvisorsDTO>>(
    `/api/v1/advisor`,
    queryParams,
  );

  return {
    items: response.administrators.map((dto) => ({
      email: dto.email,
      firstName: dto.first_name,
      id: dto.id,
      lastName: dto.last_name,
      status: dto.status,
      reachedCompetentAdviserStatus: dto.reached_competent_adviser_status,
      reachedCompetentAdviserStatusDate: dto.reached_competent_adviser_date
        ? new DateTime(dto.reached_competent_adviser_date)
        : null,
      roles: dto.roles,
    })),
    totalItems: response.total_count,
  };
};
