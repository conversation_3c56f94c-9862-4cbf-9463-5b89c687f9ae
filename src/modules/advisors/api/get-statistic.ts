import { apiClient } from '@/services/api';

interface GetStatisticDTO {
  statistic: number;
}

export enum StatisticType {
  ClientCount = 'client-count',
  ActiveAccountCount = 'active-account-count',
  OpenCaseCount = 'open-cases-count',
  MyTasksCount = 'my-tasks-count',
}

export default async (stat_name: StatisticType): Promise<number> => {
  const fetchedData = await apiClient.get<GetStatisticDTO>(
    `/api/v1/statistics/${stat_name}`,
  );
  return fetchedData.statistic;
};
