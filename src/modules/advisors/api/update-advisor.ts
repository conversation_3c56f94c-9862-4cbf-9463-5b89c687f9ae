import { apiClient } from '@/services/api';
import { DateTime } from '@/utils/dateTime';
import type { Advisor, AdvisorId } from '../types/Advisor';
import type { AdvisorDTO, UpdateAdvisorDTO } from '../dtos';

export default async (
  advisorId: AdvisorId,
  body: UpdateAdvisorDTO,
): Promise<Advisor> => {
  const response = await apiClient.put<UpdateAdvisorDTO, Promise<AdvisorDTO>>(
    `/api/v1/advisor/${advisorId}`,
    body,
  );
  return {
    id: response.id,
    firstName: response.first_name,
    lastName: response.last_name,
    email: response.email,
    roles: response.roles,
    status: response.status,
    reachedCompetentAdviserStatus: response.reached_competent_adviser_status,
    reachedCompetentAdviserStatusDate: response.reached_competent_adviser_date
      ? new DateTime(response.reached_competent_adviser_date)
      : null,
  };
};
