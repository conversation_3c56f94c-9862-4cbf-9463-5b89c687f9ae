import { onMounted, ref } from 'vue';
import { StatisticType, getStatistic } from '../api';

export default function (statistic_type: StatisticType) {
  const stat_value = ref<number>(0);

  const fetchStatistic = () => {
    return getStatistic(statistic_type).then((count: number) => {
      stat_value.value = count;
    });
  };

  onMounted(async () => {
    fetchStatistic();
  });

  return { stat_value };
}
