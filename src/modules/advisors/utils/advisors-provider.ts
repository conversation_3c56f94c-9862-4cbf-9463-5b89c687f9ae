import { storeToRefs } from 'pinia';
import { ComputedRef } from 'vue';

import { Advisor } from '../types/Advisor';
import { useAdvisorsStore } from '../stores/advisor-store';
//

export function provider() {
  async function create() {
    const { fetchAdvisors, fetchActiveAdvisors } = useAdvisorsStore();

    await fetchAdvisors();
    await fetchActiveAdvisors();
  }

  function provide(): {
    advisors: ComputedRef<Advisor[]>;
    activeAdvisors: ComputedRef<Advisor[]>;
    getAdvisorById: any;
    isBusy: ComputedRef<boolean>;
  } {
    const { getAdvisors, getActiveAdvisors, isBusy } =
      storeToRefs(useAdvisorsStore());
    const { getAdvisorById } = useAdvisorsStore();

    return {
      advisors: getAdvisors,
      activeAdvisors: getActiveAdvisors,
      getAdvisorById,
      isBusy,
    };
  }

  return {
    create,
    provide,
  };
}
