import { array, boolean, number, object, string } from 'yup';

const stringField = string().nullable().typeError('Must be valid text');
const numberField = number().nullable();
const dateField = string()
  .nullable()
  .notRequired()
  .test('is-valid-date', 'Date required', (value) => {
    if (!value) return true;
    return /^([0-9]{4})-([0-9]{2})-([0-9]{2})$/.test(value);
  });

export const validationSchemaUserText = object({
  message: stringField,
});

export const validationSchemaUser_PersonalDetails = object({
  firstName: stringField,
  lastName: stringField,
  email: stringField,
  dateOfBirth: dateField,
  phoneNumber: stringField,
  mobileNumber: stringField,
});

export const validationSchemaUser_Addresses = object({
  addresses: array().of(
    object({
      id: numberField,
      addressLineOne: stringField,
      addressLineTwo: stringField,
      addressLineThree: stringField,
      addressLineFour: stringField,
      city: stringField,
      postCode: stringField,
      country: numberField,
      movedInDate: dateField,
      movedOutDate: dateField,
      isPrimary: boolean(),
    }),
  ),
});

export const validationSchemaUser_Relations = object({
  relations: array().of(
    object({
      id: numberField,
      firstName: stringField,
      lastName: stringField,
      relationshipType: stringField,
      dateOfBirth: dateField,
    }),
  ),
});

export const validationSchema = {
  user_text_layout: validationSchemaUserText,
  user__personal_details_layout: validationSchemaUser_PersonalDetails,
  user__addresses_layout: validationSchemaUser_Addresses,
  user__relations_layout: validationSchemaUser_Relations,
};
