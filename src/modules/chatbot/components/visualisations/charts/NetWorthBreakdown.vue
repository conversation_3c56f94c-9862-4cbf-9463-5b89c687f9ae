<template>
  <ChartLayout>
    <template #chart>
      <canvas ref="histogramChartRef" />
    </template>
    <template #summaryCards>
      <Card>
        <template #label>Total Assets</template>
        <template #value>{{ formatAmount(total_assets, 0, false) }}</template>
      </Card>
      <Card>
        <template #label>Total Liabilities</template>
        <template #value>{{
          formatAmount(total_liabilities, 0, false)
        }}</template>
      </Card>
      <Card class="bg-gray-300">
        <template #label>Net Worth</template>
        <template #value>{{ formatAmount(net_worth, 0, false) }}</template>
      </Card>
    </template>
    <template #detailedBreakdown>
      <Section>
        <template #title>Assets</template>
        <template #unorderedList>
          <Item> Property: {{ formatAmount(assets.property, 0, false) }} </Item>
          <Item> Pension: {{ formatAmount(assets.pension, 0, false) }} </Item>
          <Item>
            Investments: {{ formatAmount(assets.investments, 0, false) }}
          </Item>
          <Item> Savings: {{ formatAmount(assets.savings, 0, false) }} </Item>
        </template>
      </Section>
      <Section>
        <template #title>Liabilities</template>
        <template #unorderedList>
          <Item>
            Mortgage: {{ formatAmount(liabilities.mortgage, 0, false) }}
          </Item>
          <Item>
            Other Debts: {{ formatAmount(liabilities.other_debts, 0, false) }}
          </Item>
        </template>
      </Section>
    </template>
  </ChartLayout>
</template>

<script setup lang="ts">
  import { computed, onBeforeUnmount, onMounted, ref } from 'vue';
  import colors from 'tailwindcss/colors';
  import {
    BarController,
    BarElement,
    CategoryScale,
    Chart,
    Legend,
    LinearScale,
    Title,
    Tooltip,
  } from 'chart.js';
  import { formatAmount } from '../../../utils/helpers';
  import ChartLayout from './ChartLayout.vue';
  import Section from './DetailedBreakdownSection.vue';
  import Item from './DetailedBreakdownSectionListItem.vue';
  import Card from './SummaryCardsCard.vue';

  Chart.register(
    BarElement,
    BarController,
    CategoryScale,
    LinearScale,
    Title,
    Tooltip,
    Legend,
  );

  const histogramChartRef = ref<HTMLCanvasElement | null>(null);
  let histogramChart: Chart | null = null;

  const props = defineProps<{
    schema: {
      title: string;
      total_assets: number;
      total_liabilities: number;
      net_worth: number;
      assets: {
        property: number;
        pension: number;
        investments: number;
        savings: number;
      };
      liabilities: { mortgage: number; other_debts: number };
    };
  }>();

  const title = computed(() => props.schema.title);
  const assets = computed(() => props.schema.assets);
  const liabilities = computed(() => props.schema.liabilities);
  const total_assets = computed(() => props.schema.total_assets);
  const total_liabilities = computed(() => props.schema.total_liabilities);
  const net_worth = computed(() => props.schema.net_worth);

  onMounted(() => {
    if (histogramChartRef.value && props.schema) {
      histogramChart = new Chart(histogramChartRef.value, {
        type: 'bar',
        data: {
          labels: ['Assets', 'Liabilities'],
          datasets: [
            {
              label: 'Property',
              data: [assets.value.property, 0],
              backgroundColor: '#1abc9c',
              borderColor: '#16a085',
              borderWidth: 1,
            },
            {
              label: 'Pension',
              data: [assets.value.pension, 0],
              backgroundColor: '#3498db',
              borderColor: '#2980b9',
              borderWidth: 1,
            },
            {
              label: 'Investments',
              data: [assets.value.investments, 0],
              backgroundColor: '#f1c40f',
              borderColor: '#f39c12',
              borderWidth: 1,
            },
            {
              label: 'Savings',
              data: [assets.value.savings, 0],
              backgroundColor: '#9b59b6',
              borderColor: '#8e44ad',
              borderWidth: 1,
            },
            {
              label: 'Mortgage',
              data: [0, liabilities.value.mortgage],
              backgroundColor: '#e74c3c',
              borderColor: '#c0392b',
              borderWidth: 1,
            },
            {
              label: 'Other Debts',
              data: [0, liabilities.value.other_debts],
              backgroundColor: '#e67e22',
              borderColor: '#d35400',
              borderWidth: 1,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: true,
              position: 'top',
              labels: { color: colors.black },
            },
            title: {
              display: true,
              text: title.value,
              color: colors.black,
              font: { size: 18 },
            },
            tooltip: {
              callbacks: {
                label: function (context: any) {
                  let label = context.dataset.label || '';
                  if (label) {
                    label += ': ' + formatAmount(context.parsed.y, 0, false);
                  }
                  return label;
                },
              },
              backgroundColor: colors.white,
              titleColor: colors.black,
              bodyColor: colors.black,
              borderColor: '#1abc9c',
              borderWidth: 1,
            },
          },
          scales: {
            // eslint-disable-next-line id-length
            x: {
              stacked: true,
              ticks: { color: colors.black, font: { size: 14 } },
              grid: { color: colors.gray[300] },
            },
            // eslint-disable-next-line id-length
            y: {
              stacked: true,
              beginAtZero: true,
              ticks: {
                color: colors.black,
                font: { size: 14 },
                callback: (value: string | number) =>
                  `${formatAmount(value, 0, true)}`,
              },
              grid: { color: colors.gray[300] },
            },
          },
        },
      });
    }
  });

  onBeforeUnmount(() => {
    if (histogramChart) {
      histogramChart.destroy();
    }
  });
</script>
