<template>
  <ChartLayout>
    <template #chart>
      <canvas ref="chartRef" />
    </template>
    <template #summaryCards>
      <Card>
        <template #label>Low Growth</template>
        <template #value>
          Net Worth:
          {{ formatAmount(schema.retirement_net_worth_low, 0, false) }}
        </template>
        <template #additionalValue>
          Income:
          {{ formatAmount(schema.income_low_growth, 0, false) }}
        </template>
      </Card>
      <Card>
        <template #label>Medium Growth</template>
        <template #value>
          Net Worth:
          {{ formatAmount(schema.retirement_net_worth_mid, 0, false) }}
        </template>
        <template #additionalValue>
          Income:
          {{ formatAmount(schema.income_medium_growth, 0, false) }}
        </template>
      </Card>
      <Card>
        <template #label>High Growth</template>
        <template #value>
          Net Worth:
          {{ formatAmount(schema.retirement_net_worth_high, 0, false) }}
        </template>
        <template #additionalValue>
          Income:
          {{ formatAmount(schema.income_high_growth, 0, false) }}
        </template>
      </Card>
    </template>
  </ChartLayout>
</template>

<script setup lang="ts">
  import { computed, onBeforeUnmount, onMounted, ref } from 'vue';
  import colors from 'tailwindcss/colors';
  import ChartLayout from './ChartLayout.vue';
  import Card from './SummaryCardsCard.vue';
  import {
    BarController,
    BarElement,
    CategoryScale,
    Chart,
    Legend,
    LinearScale,
    Title,
    Tooltip,
  } from 'chart.js';
  import { formatAmount } from '../../../utils/helpers';
  Chart.register(
    BarElement,
    BarController,
    CategoryScale,
    LinearScale,
    Title,
    Tooltip,
    Legend,
  );

  const chartRef = ref<HTMLCanvasElement | null>(null);
  let chart: Chart | null = null;

  const props = defineProps<{
    schema: {
      title: string;
      retirement_age: number;
      retirement_year: number;
      mortality_age: number;
      mortality_year: number;
      retirement_net_worth_low: number;
      retirement_net_worth_mid: number;
      retirement_net_worth_high: number;
      income_low_growth: number;
      income_medium_growth: number;
      income_high_growth: number;
    };
  }>();

  const title = computed(() => props.schema.title);

  onMounted(() => {
    if (chartRef.value && props.schema) {
      chart = new Chart(chartRef.value, {
        type: 'bar',
        data: {
          labels: ['Low Growth', 'Medium Growth', 'High Growth'],
          datasets: [
            {
              label: 'Net Worth',
              data: [
                props.schema.retirement_net_worth_low,
                props.schema.retirement_net_worth_mid,
                props.schema.retirement_net_worth_high,
              ],
              backgroundColor: 'rgba(66, 165, 245, 0.6)',
              borderColor: '#1e88e5',
              borderWidth: 1,
              yAxisID: 'y-networth',
            },
            {
              label: 'Income',
              data: [
                props.schema.income_low_growth,
                props.schema.income_medium_growth,
                props.schema.income_high_growth,
              ],
              backgroundColor: 'rgba(255, 193, 7, 0.6)',
              borderColor: '#ffc107',
              borderWidth: 1,
              yAxisID: 'y-income',
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: true,
              labels: { color: colors.black, font: { size: 14 } },
            },
            title: {
              display: true,
              text: title.value,
              color: colors.black,
              font: { size: 18 },
            },
            tooltip: {
              callbacks: {
                label: function (context: any) {
                  return `${context.dataset.label}: ${formatAmount(context.parsed.y, 0, false)}`;
                },
              },
              backgroundColor: colors.white,
              titleColor: colors.black,
              bodyColor: colors.black,
              borderColor: '#1abc9c',
              borderWidth: 1,
            },
          },
          scales: {
            // eslint-disable-next-line id-length
            x: {
              ticks: { color: colors.black, font: { size: 14 } },
              grid: { color: colors.gray[300] },
            },
            'y-networth': {
              type: 'linear',
              position: 'left',
              ticks: {
                color: colors.black,
                font: { size: 14 },
                callback: (value: string | number) =>
                  `${formatAmount(value, 0, true)}`,
              },
              grid: { color: colors.gray[300] },
              title: {
                display: true,
                text: 'Net Worth',
                color: colors.black,
                font: { size: 16 },
              },
            },
            'y-income': {
              type: 'linear',
              position: 'right',
              ticks: {
                color: colors.black,
                font: { size: 14 },
                callback: (value: string | number) =>
                  `${formatAmount(value, 0, true)}`,
              },
              grid: { drawOnChartArea: false },
              title: {
                display: true,
                text: 'Income',
                color: colors.black,
                font: { size: 16 },
              },
            },
          },
        },
      });
    }
  });

  onBeforeUnmount(() => {
    if (chart) {
      chart.destroy();
    }
  });
</script>
