<template>
  <ChartLayout>
    <template #chart>
      <canvas ref="lineChartRef" />
    </template>
    <template #summaryCards>
      <Card>
        <template #label>Low Growth</template>
        <template #value>{{
          formatAmount(retirementNetWorthLow, 0, false)
        }}</template>
      </Card>
      <Card>
        <template #label>Medium Growth</template>
        <template #value>{{
          formatAmount(retirementNetWorthMid, 0, false)
        }}</template>
      </Card>
      <Card>
        <template #label>High Growth</template>
        <template #value>{{
          formatAmount(retirementNetWorthHigh, 0, false)
        }}</template>
      </Card>
    </template>
    <template #detailedBreakdown>
      <Section>
        <template #title>Growth Scenarios</template>
        <template #unorderedList>
          <Item>
            Low: {{ formatAmount(lowGrowthRate, 2, false, 'percent') }}
          </Item>
          <Item>
            Medium: {{ formatAmount(mediumGrowthRate, 2, false, 'percent') }}
          </Item>
          <Item>
            High: {{ formatAmount(highGrowthRate, 2, false, 'percent') }}
          </Item>
        </template>
      </Section>
    </template>
  </ChartLayout>
</template>

<script setup lang="ts">
  import { computed, onBeforeUnmount, onMounted, ref } from 'vue';
  import colors from 'tailwindcss/colors';
  import ChartLayout from './ChartLayout.vue';
  import Section from './DetailedBreakdownSection.vue';
  import Item from './DetailedBreakdownSectionListItem.vue';
  import Card from './SummaryCardsCard.vue';
  import {
    CategoryScale,
    Chart,
    Legend,
    LineController,
    LineElement,
    LinearScale,
    PointElement,
    Title,
    Tooltip,
  } from 'chart.js';
  import { formatAmount } from '../../../utils/helpers';

  // Register necessary Chart.js components
  Chart.register(
    LineElement,
    PointElement,
    LineController,
    CategoryScale,
    LinearScale,
    Title,
    Tooltip,
    Legend,
  );

  // Reference to the canvas element
  const lineChartRef = ref<HTMLCanvasElement | null>(null);
  let lineChart: Chart | null = null;

  // Define props with investment data
  const props = defineProps<{
    schema: {
      title: string;
      years: number[];
      low_growth: number[];
      medium_growth: number[];
      high_growth: number[];
      growth_rates: {
        low: number;
        medium: number;
        high: number;
      };
      retirement_year: number;
      retirement_net_worth_low: number;
      retirement_net_worth_mid: number;
      retirement_net_worth_high: number;
    };
  }>();

  // Computed properties for easy access
  const title = computed(() => props.schema.title);
  const years = computed(() => props.schema.years);
  const lowGrowth = computed(() => props.schema.low_growth);
  const mediumGrowth = computed(() => props.schema.medium_growth);
  const highGrowth = computed(() => props.schema.high_growth);
  const growthRates = computed(() => props.schema.growth_rates);

  const retirementNetWorthLow = computed(
    () => props.schema.retirement_net_worth_low,
  );
  const retirementNetWorthMid = computed(
    () => props.schema.retirement_net_worth_mid,
  );
  const retirementNetWorthHigh = computed(
    () => props.schema.retirement_net_worth_high,
  );

  const lowGrowthRate = computed(() => growthRates.value.low);
  const mediumGrowthRate = computed(() => growthRates.value.medium);
  const highGrowthRate = computed(() => growthRates.value.high);

  // Initialize the Chart.js line chart
  onMounted(() => {
    if (lineChartRef.value && props.schema) {
      lineChart = new Chart(lineChartRef.value, {
        type: 'line',
        data: {
          labels: years.value,
          datasets: [
            {
              label: 'Low Growth',
              data: lowGrowth.value,
              borderColor: '#66bb6a',
              backgroundColor: 'rgba(102, 187, 106, 0.2)',
              borderWidth: 2,
              tension: 0.4,
              pointRadius: 4,
              pointBackgroundColor: '#66bb6a',
            },
            {
              label: 'Medium Growth',
              data: mediumGrowth.value,
              borderColor: '#42a5f5',
              backgroundColor: 'rgba(66, 165, 245, 0.2)',
              borderWidth: 2,
              tension: 0.4,
              pointRadius: 4,
              pointBackgroundColor: '#42a5f5',
            },
            {
              label: 'High Growth',
              data: highGrowth.value,
              borderColor: '#ef5350',
              backgroundColor: 'rgba(239, 83, 80, 0.2)',
              borderWidth: 2,
              tension: 0.4,
              pointRadius: 4,
              pointBackgroundColor: '#ef5350',
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: true,
              labels: { color: colors.black, font: { size: 14 } },
            },
            title: {
              display: true,
              text: title.value,
              color: colors.black,
              font: { size: 18 },
            },
            tooltip: {
              mode: 'index',
              intersect: false,
              callbacks: {
                label: function (context) {
                  return `${formatAmount(context.parsed.y, 0, false)}`;
                },
              },
              backgroundColor: colors.white,
              titleColor: colors.black,
              bodyColor: colors.black,
              borderColor: '#1abc9c',
              borderWidth: 1,
            },
          },
          interaction: {
            mode: 'nearest',
            axis: 'x',
            intersect: false,
          },
          scales: {
            // eslint-disable-next-line id-length
            x: {
              title: {
                display: true,
                text: 'Year',
                color: colors.black,
                font: { size: 14 },
              },
              ticks: { color: colors.black, font: { size: 14 } },
              grid: { color: colors.gray[300] },
            },
            // eslint-disable-next-line id-length
            y: {
              title: {
                display: true,
                text: 'Amount (£)',
                color: colors.black,
                font: { size: 14 },
              },
              beginAtZero: true,
              ticks: {
                color: colors.black,
                font: { size: 14 },
                callback: (value: string | number) =>
                  `${formatAmount(value, 0, true)}`,
              },
              grid: { color: colors.gray[300] },
            },
          },
        },
      });
    }
  });

  // Destroy the chart instance before unmounting
  onBeforeUnmount(() => {
    if (lineChart) {
      lineChart.destroy();
    }
  });
</script>
