<template>
  <BaseVisualizationLayout>
    <div v-if="$slots.chart" class="h-80 md:h-96">
      <slot name="chart" />
    </div>
    <div
      v-if="$slots.summaryCards"
      class="mb-5 mt-6 flex flex-col justify-between gap-2.5 p-0 md:mb-8 md:flex-row md:gap-5"
    >
      <slot name="summaryCards" />
    </div>
    <div
      v-if="$slots.detailedBreakdown"
      class="flex flex-col justify-between gap-2.5 md:flex-row md:gap-10"
    >
      <slot name="detailedBreakdown" />
    </div>
  </BaseVisualizationLayout>
</template>

<script setup lang="ts">
  import BaseVisualizationLayout from '../BaseVisualizationLayout.vue';
</script>
