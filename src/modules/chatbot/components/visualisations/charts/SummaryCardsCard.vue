<template>
  <div
    class="flex flex-col rounded-lg bg-gray-200 p-2.5 text-center transition-transform duration-300 hover:-translate-y-1 md:block md:flex-1 md:p-5"
  >
    <h4 class="mb-0 flex-1 text-base text-gray-800 md:mb-2.5 md:text-lg">
      <slot name="label" />
    </h4>
    <p v-if="$slots.value" class="flex-1 text-base font-bold text-gray-800">
      <slot name="value" />
    </p>
    <p
      v-if="$slots.additionalValue"
      class="flex-1 text-base font-bold text-gray-800"
    >
      <slot name="additionalValue" />
    </p>
  </div>
</template>
