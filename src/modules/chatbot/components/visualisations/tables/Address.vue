<template>
  <Table>
    <template #tableHeaderCol1>Address</template>
    <template #tableBody>
      <tr
        v-for="[key, value] in schema"
        :key="key"
        class="even:bg-gray-100 hover:bg-gray-200"
      >
        <Td>{{ formatToHammerCase(String(key)) }}</Td>
        <Td v-if="key === 'moved_in_date' || key === 'moved_out_date'">
          {{ formatToUKDateIntl(String(value)) }}
        </Td>
        <Td v-else>{{ value }}</Td>
      </tr>
    </template>
  </Table>
</template>

<script setup lang="ts">
  import Table from './DesktopTable.vue';
  import Td from './DesktopTableDataCell.vue';
  import { computed } from 'vue';
  import {
    formatToHammerCase,
    formatToUKDateIntl,
    stripEmptyValues,
  } from '../../../utils/helpers';
  import { AllFormValues } from '@modules/chatbot/types/form-model';

  const props = defineProps<{
    message: AllFormValues;
  }>();

  const schema = computed(() => stripEmptyValues(props.message));
</script>
