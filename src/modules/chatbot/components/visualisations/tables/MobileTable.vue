<template>
  <BaseVisualizationLayout>
    <p
      class="border-b-2 border-solid border-gray-400 px-4 py-2.5 text-left font-bold"
    >
      <slot name="title"></slot>
    </p>
    <div class="flex flex-col">
      <div
        v-for="(item, index) in props.lineItems"
        :key="index"
        class="border-b-2 border-solid border-gray-400 bg-white"
      >
        <div
          v-for="(field, fieldIndex) in props.formattedFields(item)"
          :key="fieldIndex"
          :class="[
            'flex px-4 py-2.5 hover:bg-gray-200',
            { 'bg-gray-100': fieldIndex % 2 !== 0 },
          ]"
        >
          <span class="flex-1 break-words text-left">{{ field.label }}</span>
          <span class="flex-1 break-words text-left">{{ field.value }}</span>
        </div>
      </div>
    </div>
  </BaseVisualizationLayout>
</template>

<script setup lang="ts">
  import { AddressFormValue } from '@modules/chatbot/types/form-model';
  import BaseVisualizationLayout from '../BaseVisualizationLayout.vue';
  import { Nullable } from '@/types/Common';

  const props = defineProps<{
    lineItems: Nullable<AddressFormValue[]>;
    formattedFields: (item: Nullable<AddressFormValue>) => {
      label: string;
      value:
        | Nullable<string>
        | Nullable<number>
        | Nullable<boolean>
        | undefined;
    }[];
  }>();
</script>
