<template>
  <Table>
    <template #tableHeaderCol1>Personal Details</template>
    <template #tableBody>
      <tr
        v-for="[key, value] in schema"
        :key="key"
        class="even:bg-gray-100 hover:bg-gray-200"
      >
        <Td>{{ formatToHammerCase(String(key)) }}</Td>
        <Td v-if="key === 'date_of_birth'">
          {{ formatToUKDateIntl(String(value)) }}
        </Td>
        <Td v-else-if="key === 'email'" style="word-break: break-word">
          {{ value }}
        </Td>
        <Td v-else>{{ value }}</Td>
      </tr>
    </template>
  </Table>
</template>

<script setup lang="ts">
  import Table from './DesktopTable.vue';
  import Td from './DesktopTableDataCell.vue';
  import {
    formatToHammerCase,
    formatToUKDateIntl,
    stripEmptyValues,
  } from '../../../utils/helpers';
  import { computed } from 'vue';
  import { AllFormValues } from '@modules/chatbot/types/form-model';

  const props = defineProps<{
    message: AllFormValues;
  }>();

  const schema = computed(() => stripEmptyValues(props.message));
</script>
