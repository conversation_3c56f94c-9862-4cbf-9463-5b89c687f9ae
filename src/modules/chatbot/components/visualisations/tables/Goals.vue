<template>
  <Table :title-colspan="1">
    <template #tableHeaderCol1>Goal</template>
    <template #tableHeaderCol2>Description</template>
    <template #tableBody>
      <tr
        v-for="(goal, index) in props.schema.goals"
        :key="index"
        class="even:bg-gray-100 hover:bg-gray-200"
      >
        <Td>{{ goal.name }}</Td>
        <Td>{{ goal.description }}</Td>
      </tr>
    </template>
  </Table>
</template>

<script setup lang="ts">
  import Table from './DesktopTable.vue';
  import Td from './DesktopTableDataCell.vue';

  const props = defineProps<{
    schema: { goals: { name: string; description: string }[] };
  }>();
</script>
