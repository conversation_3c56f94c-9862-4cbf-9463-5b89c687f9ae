<template>
  <BaseVisualizationLayout>
    <table class="w-full border-collapse border-spacing-0">
      <thead>
        <tr>
          <Th class="break-words px-4 py-2.5 text-left" :colspan="titleColspan">
            <slot name="tableHeaderCol1" />
          </Th>
          <Th v-if="$slots.tableHeaderCol2">
            <slot name="tableHeaderCol2" />
          </Th>
        </tr>
        <tr v-if="$slots.tableHeaderSecond">
          <slot name="tableHeaderSecond" />
        </tr>
      </thead>
      <tbody>
        <slot name="tableBody" />
      </tbody>
    </table>
  </BaseVisualizationLayout>
</template>

<script setup lang="ts">
  import BaseVisualizationLayout from '../BaseVisualizationLayout.vue';
  import Th from './DesktopTableTableHeader.vue';

  defineProps({
    titleColspan: {
      type: Number,
      default: 2,
    },
  });
</script>
