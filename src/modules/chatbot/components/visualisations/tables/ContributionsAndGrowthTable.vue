<template>
  <Table>
    <template #tableHeaderCol1>Contributions and Growth</template>
    <template #tableHeaderSecond>
      <Th>Category</Th>
      <Th>Value</Th>
    </template>
    <template #tableBody>
      <tr
        v-for="[key, value] in schema"
        :key="key"
        class="even:bg-gray-100 hover:bg-gray-200"
      >
        <Td>{{ formatToHammerCase(String(key)) }}</Td>
        <Td v-if="key === 'desired_retirement_age'">{{ value }}</Td>
        <Td v-else-if="key === 'desired_savings_buffer'">
          {{ formatAmount(Number(value), 0, false) }}
        </Td>
        <Td v-else>{{ formatAmount(Number(value), 2, false, 'percent') }}</Td>
      </tr>
    </template>
  </Table>
</template>

<script setup lang="ts">
  import Table from './DesktopTable.vue';
  import Th from './DesktopTableTableHeader.vue';
  import Td from './DesktopTableDataCell.vue';

  import {
    formatAmount,
    formatToHammerCase,
    stripEmptyValues,
  } from '../../../utils/helpers';
  import { computed } from 'vue';
  import { AllFormValues } from '@modules/chatbot/types/form-model';

  const props = defineProps<{
    message: AllFormValues;
  }>();

  const schema = computed(() => stripEmptyValues(props.message));
</script>
