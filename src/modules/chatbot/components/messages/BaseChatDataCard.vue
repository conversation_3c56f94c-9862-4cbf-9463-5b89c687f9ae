<template>
  <div class="flex">
    <div v-if="isUser" class="min-w-16 grow" />
    <div class="flex flex-col gap-2" :class="containerClasses">
      <h1 class="font-bold">{{ props.title }}</h1>
      <slot />
    </div>
    <div v-if="!isUser" class="min-w-16 grow" />
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';

  interface Props {
    title: string;
    isUser?: boolean;
    width?: 'default' | 'full';
    padding?: '2' | '3' | '4' | '6';
    variant?: 'default' | 'compact';
  }

  const props = withDefaults(defineProps<Props>(), {
    isUser: false,
    width: 'default',
    padding: '2',
    variant: 'default',
  });

  const containerClasses = computed(() => {
    const classes = [
      'mb-2',
      'rounded-lg',
      'border',
      'border-gray-200',
      'bg-white',
      'flex-shrink-0',
    ];

    if (props.variant === 'default') {
      classes.push('shadow-sm');
    }

    // Padding
    classes.push(`p-${props.padding}`);

    // Width
    if (props.width === 'full') {
      classes.push('w-full');
    } else {
      classes.push('w-[87%]');
    }

    // Alignment (only apply auto margins if not full width)
    if (props.width !== 'full') {
      if (props.isUser) {
        classes.push('ml-auto');
      } else {
        classes.push('mr-auto');
      }
    }
    return classes.join(' ');
  });
</script>
