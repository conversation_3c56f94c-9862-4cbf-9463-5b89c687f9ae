<template>
  <div class="flex">
    <p
      class="mb-2 mr-auto whitespace-pre-wrap rounded-lg border border-gray-800/10 px-4 py-2 text-sm font-medium text-gray-800"
    >
      {{ formattedKey }}: {{ value }}
    </p>
    <div class="min-w-16 grow" />
  </div>
</template>

<script setup lang="ts">
  import { AssistantTextFormValues } from '../../types/form-model';

  function camelToTitleCase(camelCaseStr: string): string {
    if (!camelCaseStr) {
      return '';
    }
    const resultWithSpaces = camelCaseStr.replace(/([A-Z])/g, ' $1');
    return resultWithSpaces.charAt(0).toUpperCase() + resultWithSpaces.slice(1);
  }

  const props = defineProps<{
    message: AssistantTextFormValues;
  }>();

  const { id, timestamp, messageLayout, ...otherProps } = props.message;

  const keys = Object.keys(otherProps) as Array<keyof typeof otherProps>;

  if (keys.length === 0) {
    console.warn('Warning: No "other" properties found to log.', otherProps);
  }

  if (keys.length > 1) {
    console.warn(
      "Warning: More than one 'other' property found. Logging only the first one.",
    );
  }

  const otherKey = keys[0];

  const formattedKey = camelToTitleCase(otherKey as string);
  let value = otherProps[otherKey];
  if (value === null) {
    value = 'No data';
  }
  if (value === '') {
    value = '(Empty String)';
  }
</script>
