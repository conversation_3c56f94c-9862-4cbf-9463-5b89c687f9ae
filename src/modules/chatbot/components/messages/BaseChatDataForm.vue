<template>
  <BaseChatDataCard
    :title="config.title"
    :is-user="isUser"
    :width="width"
    :padding="padding"
    :variant="variant"
  >
    <form @submit.prevent="handleSubmit" data-testid="chat-data-form">
      <fieldset class="flex flex-col gap-2" :disabled="message.completed">
        <!-- Dynamic field rendering -->
        <div class="mb-1 grid grid-cols-[minmax(0,33%)_1fr] items-center gap-2">
          <template v-for="field in visibleFields" :key="field.key">
            <!-- Label for non-array fields -->
            <label
              v-if="field.type !== 'array'"
              :for="field.key"
              class="hyphens-auto break-words text-right text-sm font-medium"
            >
              {{ field.label }}:
            </label>

            <!-- Regular Field -->
            <div v-if="field.type !== 'array'" class="flex flex-col">
              <DynamicField
                :field="field"
                :model-value="getFieldValue(field.key)"
                :disabled="!isEditMode || message.completed || field.disabled"
                :readonly="!isEditMode || message.completed || field.readonly"
              />
            </div>

            <!-- Array Field -->
            <div
              v-else-if="field.type === 'array'"
              class="col-span-2 flex flex-col"
            >
              <div
                v-for="(item, index) in getFieldValue(field.key) || []"
                :key="index"
                :class="[
                  'grid grid-cols-[minmax(0,33%)_1fr] items-center gap-2',
                  {
                    'border-b border-gray-300':
                      index < (getFieldValue(field.key) || []).length - 1,
                  },
                ]"
              >
                <Field
                  type="hidden"
                  :name="`${field.key}[${index}].id`"
                  :model-value="item.id ?? null"
                />

                <template
                  v-for="itemField in getArrayItemFields(field)"
                  :key="itemField.key"
                >
                  <label
                    :for="`${field.key}[${index}].${itemField.key}`"
                    class="hyphens-auto break-words text-right text-sm font-medium"
                  >
                    {{ itemField.label }}:
                  </label>

                  <div class="flex flex-col">
                    <DynamicField
                      :field="itemField"
                      :model-value="getArrayItemFieldValue(item, itemField.key)"
                      :disabled="
                        !isEditMode || message.completed || itemField.disabled
                      "
                      :readonly="
                        !isEditMode || message.completed || itemField.readonly
                      "
                      :field-name="`${field.key}[${index}].${itemField.key}`"
                      :field-id="`${field.key}[${index}].${itemField.key}`"
                      @change="
                        (value) =>
                          handleArrayItemFieldChange(
                            itemField.key,
                            value,
                            index,
                          )
                      "
                    />
                  </div>
                </template>
              </div>
            </div>
          </template>
        </div>

        <!-- Edit/Cancel/Save Buttons -->
        <div class="flex justify-end gap-2">
          <!-- Edit Button (read-only mode, not completed) -->
          <BaseButton
            class="h-10 w-full !rounded-[20px]"
            v-if="!isEditMode && !message.completed"
            @click="enableEditMode"
            theme="secondary"
            data-testid="edit-button"
          >
            Edit
          </BaseButton>

          <!-- Cancel Button (edit mode) -->
          <BaseButton
            class="h-10 flex-1 !rounded-[20px]"
            v-if="isEditMode"
            @click="cancelEdit"
            theme="secondary"
            :is-busy="isCancelLoading"
            :disabled="isConfirmLoading"
            data-testid="cancel-button"
          >
            Cancel
          </BaseButton>

          <!-- Submit Button (edit mode) -->
          <BaseButton
            class="h-10 flex-1 !rounded-[20px]"
            v-if="isEditMode"
            type="submit"
            :is-busy="isConfirmLoading"
            :disabled="isCancelLoading"
            theme="primary"
            data-testid="confirm-button"
          >
            Confirm
          </BaseButton>
        </div>
      </fieldset>
    </form>
  </BaseChatDataCard>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { Field, useForm } from 'vee-validate';
  import { useAPIState } from '@/composables/useAPIState';
  import BaseButton from '@/components/BaseButton.vue';
  import BaseChatDataCard from '@modules/chatbot/components/messages/BaseChatDataCard.vue';
  import DynamicField from '@modules/chatbot/components/messages/DynamicField.vue';
  import {
    FieldHandlers,
    FieldOverrides,
    FormConfig,
    FormFieldConfig,
    FormMessage,
    FormSubmitHandler,
    HttpMethod,
    getFieldsFromSchema,
  } from '@modules/chatbot/types/form-config';

  interface Props {
    config: FormConfig;
    message: FormMessage;
    onSubmit: FormSubmitHandler;
    validationSchema?: any;
    initialValues?: any;
    isUser?: boolean;
    width?: 'default' | 'full';
    padding?: '2' | '3' | '4' | '6';
    variant?: 'default' | 'compact';
    fieldOverrides?: FieldOverrides;
    fieldHandlers?: FieldHandlers;
  }

  const props = withDefaults(defineProps<Props>(), {
    isUser: false,
    width: 'default',
    padding: '2',
    variant: 'default',
  });

  const { isLoading } = useAPIState();

  // Extract data fields from message
  const {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    id,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    timestamp,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    messageLayout,
    availableFields,
    method,
    completed,
    ...dataFields
  } = props.message;

  const isEditMode = ref(method !== HttpMethod.GET && !completed);
  const originalValues = ref<any>({});
  const isCancelLoading = ref(false);
  const isConfirmLoading = ref(false);

  // Generate fields from validation schema
  const visibleFields = computed(() => {
    if (!props.validationSchema) {
      return [];
    }

    const fields = getFieldsFromSchema(
      props.validationSchema,
      availableFields,
    ).filter((field) => !field.hidden);

    // Apply field overrides
    return fields.map((field) => {
      const override = props.fieldOverrides?.[field.key];
      if (override) {
        return {
          ...field,
          ...(override.type && { type: override.type }),
          ...(override.label && { label: override.label }),
          ...(override.options && { options: override.options }),
        };
      }
      return field;
    });
  });

  /**
   * Determines the appropriate field type for rendering by checking explicit overrides first,
   * then falling back to schema-based type detection from Yup validation tests.
   * This enables automatic field type detection while allowing manual overrides when needed.
   */
  const getFieldTypeWithOverrides = (
    fieldSchema: any,
    fieldKey?: string,
  ): FormFieldConfig['type'] | 'array' | null => {
    // Check prop-based overrides first
    if (fieldKey && props.fieldOverrides?.[fieldKey]?.type) {
      return props.fieldOverrides[fieldKey].type;
    }

    const type = fieldSchema?.type || fieldSchema?.spec?.type;

    if (type === 'array') {
      return 'array';
    }

    switch (type) {
      case 'string':
        if (fieldSchema?.tests?.some((test: any) => test.name === 'email')) {
          return 'email';
        }
        if (
          fieldSchema?.tests?.some((test: any) => test.name === 'is-valid-date')
        ) {
          return 'date';
        }
        return 'text';
      case 'number':
        return 'text';
      case 'boolean':
        return 'checkbox';
      default:
        return 'text';
    }
  };

  /**
   * Extracts and configures field definitions for array items from the schema.
   * Applies field overrides and generates proper labels for each item field,
   * excluding ID fields which should remain hidden.
   */
  const getArrayItemFields = (field: FormFieldConfig): FormFieldConfig[] => {
    if (!field.arrayItemSchema?.fields) {
      return [];
    }

    const fields: FormFieldConfig[] = [];
    Object.entries(field.arrayItemSchema.fields).forEach(
      ([key, fieldSchema]: [string, any]) => {
        // Skip ID fields - they should be hidden
        if (key === 'id') {
          return;
        }

        const fieldType = getFieldTypeWithOverrides(fieldSchema, key);
        if (fieldType && fieldType !== 'array') {
          const baseField: FormFieldConfig = {
            key,
            type: fieldType as FormFieldConfig['type'],
            label: formatFieldLabel(key),
            required: isFieldRequired(fieldSchema),
          };

          // Apply field overrides if they exist
          const override = props.fieldOverrides?.[key];
          if (override) {
            if (override.type) baseField.type = override.type;
            if (override.label) baseField.label = override.label;
            if (override.options) baseField.options = override.options;
          }

          fields.push(baseField);
        }
      },
    );

    return fields;
  };

  // Helper functions for array field rendering
  const isFieldRequired = (fieldSchema: any): boolean => {
    try {
      return (
        fieldSchema?.tests?.some((test: any) => test.name === 'required') ||
        false
      );
    } catch {
      return false;
    }
  };

  const formatFieldLabel = (fieldKey: string): string => {
    return fieldKey
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase())
      .trim();
  };

  /**
   * Retrieves a field value from an array item object, with fallback to default values.
   * Assumes data is in camelCase format (e.g., 'movedInDate' not 'moved_in_date').
   * Returns the field's default value from overrides if the actual value is null/undefined.
   * Used for populating form fields in array contexts like address lists.
   */
  const getArrayItemFieldValue = (item: any, fieldKey: string): any => {
    const value = item[fieldKey];

    if (value !== null && value !== undefined) {
      return value;
    }

    // Check for default value in overrides
    const override = props.fieldOverrides?.[fieldKey];
    return override?.defaultValue ?? undefined;
  };

  /**
   * Handles field value changes for array items by invoking custom field handlers.
   * Enables specialized behavior like setting primary flags or validation logic
   * for specific fields within array contexts (e.g., isPrimary checkbox in addresses).
   * Only executes if a custom handler is defined for the field in fieldHandlers prop.
   */
  const handleArrayItemFieldChange = (
    fieldKey: string,
    value: any,
    index: number,
  ): void => {
    const handler = props.fieldHandlers?.[fieldKey];
    if (handler?.onChange) {
      handler.onChange(value, index);
    }
  };

  // Get field value from message data (assumes camelCase)
  const getFieldValue = (fieldKey: string) => {
    // For addresses, check if data is directly an array
    if (fieldKey === 'addresses' && Array.isArray(dataFields.data)) {
      return dataFields.data;
    }

    const value = dataFields[fieldKey];

    // If value is null/undefined, check for default value in overrides
    if (value === null || value === undefined) {
      const override = props.fieldOverrides?.[fieldKey];
      if (override?.defaultValue !== undefined) {
        return override.defaultValue;
      }
    }

    return value;
  };

  // Setup form validation
  const { handleSubmit: veeHandleSubmit, setValues } = useForm({
    validationSchema: props.validationSchema,
    initialValues: props.initialValues,
  });

  // Edit mode methods
  const enableEditMode = () => {
    if (completed) return;
    isEditMode.value = true;
    // Store deep copy of current form values for restoration
    originalValues.value = JSON.parse(JSON.stringify(dataFields));
  };

  const cancelEdit = async () => {
    isCancelLoading.value = true;
    try {
      if (method === HttpMethod.GET) {
        // Restore original values using vee-validate
        setValues(originalValues.value);
      } else {
        // Mark the message as completed, but with no changes made
        props.message.completed = true;
        props.message.changesMade = false;
        await props.onSubmit(props.message);
      }
      isEditMode.value = false;
    } finally {
      isCancelLoading.value = false;
    }
  };

  const handleSubmit = veeHandleSubmit(async (formValues) => {
    isConfirmLoading.value = true;
    try {
      props.message.completed = true;
      props.message.changesMade = true;
      Object.assign(props.message, formValues);
      await props.onSubmit(formValues);
      isEditMode.value = false;
    } finally {
      isConfirmLoading.value = false;
    }
  });
</script>
