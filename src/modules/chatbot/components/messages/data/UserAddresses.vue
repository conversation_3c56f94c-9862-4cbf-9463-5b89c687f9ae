<template>
  <BaseChatDataForm
    :config="formConfig"
    :message="props.message"
    :validation-schema="validationSchema['user__addresses_layout']"
    :initial-values="getInitialValues('user__addresses_layout')"
    :on-submit="handleFormSubmit"
    :field-overrides="fieldOverrides"
    :field-handlers="fieldHandlers"
  />
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { useForm } from 'vee-validate';
  import { useChatbotStore } from '../../../stores/chatbotStore';
  import {
    User_Addresses_FormValues,
    getInitialValues,
  } from '@modules/chatbot/types/form-model';
  import { validationSchema } from '../../../services/validation/form-validation';
  import BaseChatDataForm from '@modules/chatbot/components/messages/BaseChatDataForm.vue';
  import {
    FieldHandlers,
    FieldOverrides,
    FormConfig,
    HttpMethod,
  } from '@modules/chatbot/types/form-config';
  import { sortBy } from 'lodash';
  import { SelectOption } from '@/components/form/fields/field-model';
  import { useRefData } from '@/stores';

  const { getCountries, getCountryByCode } = useRefData();

  const chatbotStore = useChatbotStore();
  const { postMessage } = chatbotStore;

  const props = defineProps<{
    message: User_Addresses_FormValues;
  }>();

  // Extract meta-fields; the rest (addresses array) go into dataFields
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { id, timestamp, messageLayout, availableFields, ...dataFields } =
    props.message;

  const defaultCountryId: number = getCountryByCode('GBR')?.id;

  const countries = sortBy(
    getCountries.map((item) => ({
      label: item.name,
      value: item.id,
    })),
    'label',
  ) as Array<SelectOption>;

  // Initialize vee-validate form with the correct schema & initial values
  const { setFieldValue } = useForm<User_Addresses_FormValues>({
    validationSchema: validationSchema['user__addresses_layout'],
    initialValues: getInitialValues('user__addresses_layout'),
  });

  function setPrimary(selectedIndex: number) {
    props.message.addresses!.forEach((_, idx) => {
      // this updates both Vee-Validate's form state *and* the Vue template
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      setFieldValue(`addresses.${idx}.isPrimary`, idx === selectedIndex);
    });
  }

  // Field overrides for custom behavior
  const fieldOverrides = computed<FieldOverrides>(() => ({
    country: {
      type: 'select',
      options: countries,
      defaultValue: defaultCountryId,
      label: 'Country',
    },
    movedInDate: {
      type: 'date',
    },
    movedOutDate: {
      type: 'date',
    },
  }));

  // Field handlers for custom logic
  const fieldHandlers = computed<FieldHandlers>(() => ({
    isPrimary: {
      onChange: (value: boolean, index?: number) => {
        if (value && index !== undefined) {
          setPrimary(index);
        }
      },
    },
  }));

  // Form configuration - empty fields since we use slots for the complex address rendering
  const formConfig = computed<FormConfig>(() => ({
    title:
      props.message.method !== HttpMethod.GET
        ? 'Confirm Change'
        : 'Requested Details',
    validationSchema: 'user__addresses_layout',
  }));

  const handleFormSubmit = async (formValues: User_Addresses_FormValues) => {
    // if the user used the "clear" button on the datepicker, ensure the new value is undefined, not an empty string
    for (const address of formValues.addresses!) {
      if (address.movedInDate == '') {
        address.movedInDate = undefined;
      }
      if (address.movedOutDate == '') {
        address.movedOutDate = undefined;
      }
    }

    await postMessage(props.message);
  };
</script>
