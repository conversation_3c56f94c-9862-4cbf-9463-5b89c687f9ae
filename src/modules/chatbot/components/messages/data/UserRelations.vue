<template>
  <BaseChatDataForm
    :config="formConfig"
    :message="props.message"
    :validation-schema="validationSchema['user__relations_layout']"
    :initial-values="getInitialValues('user__relations_layout')"
    :on-submit="handleFormSubmit"
    :field-overrides="fieldOverrides"
  />
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { useChatbotStore } from '../../../stores/chatbotStore';
  import { relationshipTypeSelectOptions } from '@modules/factfind/models';
  import {
    User_Relations_FormValues,
    getInitialValues,
  } from '../../../types/form-model';
  import { validationSchema } from '../../../services/validation/form-validation';
  import BaseChatDataForm from '@modules/chatbot/components/messages/BaseChatDataForm.vue';
  import { FormConfig, HttpMethod } from '@modules/chatbot/types/form-config';

  const chatbotStore = useChatbotStore();
  const { postMessage } = chatbotStore;

  const props = defineProps<{
    message: User_Relations_FormValues;
  }>();

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { id, timestamp, messageLayout, availableFields, ...dataFields } =
    props.message;

  // Form configuration
  const formConfig = computed<FormConfig>(() => ({
    title:
      props.message.method !== HttpMethod.GET ? 'Confirm Change' : 'Relations',
    validationSchema: 'user__relations_layout',
  }));

  // Field overrides for select options
  const fieldOverrides = {
    relationshipType: {
      type: 'select' as const,
      options: relationshipTypeSelectOptions,
    },
    dateOfBirth: {
      type: 'date' as const,
    },
  };

  const handleFormSubmit = async (formValues: User_Relations_FormValues) => {
    await postMessage(props.message);
  };
</script>
