<template>
  <BaseChatDataCard title="Your Overall Health Score" :is-user="false">
    <div class="flex flex-col items-center space-y-6">
      <!-- Overall Score -->
      <div class="mt--2 h-24 w-48">
        <score-gauge
          data-testid="score-gauge"
          :score="healthScoreData.overallScore ?? 0"
          :max-score="1000"
        />
      </div>

      <!-- Goal Scores -->
      <div
        v-if="healthScoreData.goalScores && healthScoreData.goalScores.length"
        data-testid="goals-section"
        class="mt-2 w-full space-y-4"
      >
        <div
          v-for="(goal, index) in healthScoreData.goalScores"
          :key="index"
          :data-testid="`goal-${index}`"
          class="overflow-hidden rounded-lg border bg-gray-50 shadow-sm"
        >
          <!-- Header Section -->
          <div class="flex items-center justify-between px-4 py-3">
            <div>
              <p
                data-testid="goal-name"
                class="text-sm font-semibold text-gray-800"
              >
                {{ goal.name }}
              </p>
              <p
                v-if="
                  goal.targetDate ||
                  (goal.type === 'Retirement' && goal.cashflowTimeseries)
                "
                data-testid="goal-date"
                class="text-xs text-gray-600"
              >
                {{
                  (goal.type === 'Retirement' && goal.cashflowTimeseries
                    ? goal.cashflowTimeseries[0][
                        goal.cashflowTimeseries[0].length - 1
                      ]
                    : goal.targetDate
                  )?.toLocaleDateString('en-GB', {
                    month: 'long',
                    year: 'numeric',
                  })
                }}
              </p>
            </div>
            <div class="size-12">
              <score-donut :score="goal.score ?? 0" />
            </div>
          </div>

          <!-- Error Message for Goal (outside cashflow check) -->
          <div v-if="goal.error" class="px-4 pb-3">
            <div class="rounded-lg border border-red-200 bg-red-50 p-3">
              <p
                data-testid="goal-error"
                class="text-xs font-medium text-red-700"
              >
                {{ goal.error }}
              </p>
            </div>
          </div>

          <!-- Content Section -->
          <div v-if="goal.cashflowTimeseries" class="px-4 pb-3">
            <!-- Cashflow Timeseries Chart -->
            <div class="mb-3 flex gap-4">
              <div>
                <p class="mb-1 text-xs font-medium text-gray-500">Projection</p>
                <div
                  data-testid="projection-amount"
                  :class="`${getProjectionPillClasses(goal.cashflowTimeseries[1][goal.cashflowTimeseries[1].length - 1], goal.targetAmountInflated || goal.targetAmount)} px-3 py-1 rounded-full text-sm font-bold`"
                >
                  {{
                    formatPillCurrency(
                      Math.round(
                        goal.cashflowTimeseries[1][
                          goal.cashflowTimeseries[1].length - 1
                        ],
                      ),
                    )
                  }}
                </div>
              </div>
              <div v-if="goal.targetAmount">
                <p class="mb-1 text-xs font-medium text-gray-500">
                  Target (+inflation)
                </p>
                <div
                  data-testid="target-amount"
                  :class="getTargetPillClasses()"
                  :style="getTargetPillStyle()"
                  class="flex items-center"
                >
                  <span data-testid="target-base-amount">{{
                    formatPillCurrency(Math.round(goal.targetAmount))
                  }}</span>
                  <div
                    v-if="goal.targetAmountInflated"
                    class="mx-2 h-4 w-px bg-purple-300"
                  ></div>
                  <span
                    v-if="goal.targetAmountInflated"
                    data-testid="target-inflation-amount"
                    >+{{
                      formatPillCurrency(
                        Math.round(
                          goal.targetAmountInflated - goal.targetAmount,
                        ),
                      )
                    }}</span
                  >
                </div>
              </div>
            </div>
            <div class="h-32">
              <canvas
                :ref="(el) => setChartRef(el, index)"
                class="size-full"
              ></canvas>
            </div>
          </div>

          <!-- Holding Warnings (outside cashflow check) -->
          <div
            v-if="goal.holdingWarnings && goal.holdingWarnings.length"
            class="px-4 pb-3"
          >
            <div
              class="rounded-lg border border-yellow-200 bg-yellow-50 p-3"
              data-testid="holding-warnings"
            >
              <p
                data-testid="holding-warnings-title"
                class="mb-2 text-sm font-medium text-yellow-800"
              >
                Warnings:
              </p>
              <ul
                class="list-outside list-disc space-y-1 pl-4 text-xs text-yellow-700"
              >
                <li
                  v-for="(warning, wIndex) in goal.holdingWarnings"
                  :key="wIndex"
                  data-testid="holding-warning-item"
                >
                  <span class="font-semibold">{{ warning.productType }}</span>
                  - {{ warning.providerName }} (Account:
                  {{ warning.accountNumber }}):
                  {{ warning.warning }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BaseChatDataCard>
</template>

<script setup lang="ts">
  import { onBeforeUnmount, onMounted, ref } from 'vue';
  import {
    CategoryScale,
    Chart,
    Filler,
    LineController,
    LineElement,
    LinearScale,
    PointElement,
    TimeScale,
    Title,
    Tooltip,
  } from 'chart.js';
  import 'chartjs-adapter-date-fns';
  import ScoreGauge from '@/components/score/score-gauge.vue';
  import ScoreDonut from '@/components/score/score-donut.vue';
  import { User_HealthScore_FormValues } from '../../../types/form-model';
  import BaseChatDataCard from '@modules/chatbot/components/messages/BaseChatDataCard.vue';

  // Register Chart.js components
  Chart.register(
    LineController,
    LineElement,
    PointElement,
    CategoryScale,
    LinearScale,
    TimeScale,
    Title,
    Tooltip,
    Filler,
  );

  const props = defineProps<{
    message: User_HealthScore_FormValues;
  }>();
  const {
    id: _id,
    timestamp: _timestamp,
    messageLayout: _messageLayout,
    availableFields: _availableFields,
    ...healthScoreData
  } = props.message;

  const chartRefs = ref<Map<number, HTMLCanvasElement>>(new Map());
  const charts = ref<Map<number, Chart<'line'>>>(new Map());

  const getProjectionPillClasses = (
    projectedAmount: number,
    targetAmount: number | null,
  ) => {
    if (!targetAmount) return 'bg-green-100 text-green-700';
    return projectedAmount >= targetAmount
      ? 'bg-green-100 text-green-700'
      : 'bg-red-100 text-red-700';
  };

  const getTargetPillClasses = () =>
    'bg-purple-100 px-3 py-1 rounded-full text-sm font-bold';
  const getTargetPillStyle = () => ({ color: '#8B5CF6' });

  const setChartRef = (el: any, index: number) => {
    if (el && el instanceof HTMLCanvasElement) {
      chartRefs.value.set(index, el);
    }
  };

  const formatCurrency = (value: number): string => {
    if (value >= 1000000) {
      return `£${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `£${(value / 1000).toFixed(0)}K`;
    } else {
      return `£${value}`;
    }
  };

  const formatPillCurrency = (value: number): string => {
    if (value > 99000) {
      return formatCurrency(value);
    } else {
      return `£${value.toLocaleString()}`;
    }
  };

  const createChart = (
    canvas: HTMLCanvasElement,
    dates: Date[],
    values: number[],
  ) => {
    const _ctx = canvas.getContext('2d')!;

    return new Chart(canvas, {
      type: 'line',
      data: {
        labels: dates,
        datasets: [
          {
            data: values,
            borderColor: '#A78BFA',
            backgroundColor: (context) => {
              const chart = context.chart;
              const { ctx: chartCtx, chartArea } = chart;
              if (!chartArea) return 'rgba(194, 174, 253, 0.2)';
              const gradient = chartCtx.createLinearGradient(
                0,
                chartArea.top,
                0,
                chartArea.bottom,
              );
              gradient.addColorStop(0, 'rgba(194, 174, 253, 0.5)');
              gradient.addColorStop(1, 'rgba(194, 174, 253, 0.1)');
              return gradient;
            },
            borderWidth: 3,
            tension: 0.4,
            pointRadius: 0,
            fill: 'origin',
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: { display: false },
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              title: (context) => {
                const date = context[0].parsed.x;
                return new Date(date).toLocaleDateString('en-GB', {
                  day: 'numeric',
                  month: 'short',
                  year: 'numeric',
                });
              },
              label: (context) => {
                return `£${Number(context.parsed.y).toLocaleString()}`;
              },
            },
          },
        },
        scales: {
          // eslint-disable-next-line id-length
          x: {
            type: 'time',
            time: {
              unit: 'month',
              displayFormats: {
                month: 'MMM yy',
              },
            },
            ticks: { maxTicksLimit: 6, font: { size: 10 }, color: '#9CA3AF' },
            border: { display: false },
          },
          // eslint-disable-next-line id-length
          y: {
            ticks: {
              callback: (value: string | number) =>
                formatCurrency(Number(value)),
              font: { size: 10 },
              color: '#9CA3AF',
            },
            border: { display: false },
          },
        },
      },
    });
  };

  onMounted(async () => {
    if (healthScoreData.goalScores) {
      healthScoreData.goalScores.forEach((goal, index) => {
        if (goal.cashflowTimeseries) {
          const canvas = chartRefs.value.get(index);
          if (canvas) {
            const [dates, values] = goal.cashflowTimeseries;
            const chart = createChart(canvas, dates, values);
            charts.value.set(index, chart);
          }
        }
      });
    }
  });

  onBeforeUnmount(() => {
    charts.value.forEach((chart) => chart.destroy());
  });
</script>
