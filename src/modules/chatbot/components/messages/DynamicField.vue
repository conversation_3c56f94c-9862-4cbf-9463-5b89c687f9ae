<template>
  <Field
    :name="fieldName"
    :model-value="modelValue"
    v-slot="{ field: fieldProps, errorMessage }"
  >
    <!-- Text/Email Field -->
    <TextField
      v-if="field.type === 'text' || field.type === 'email'"
      class="!mb-0 w-full"
      v-bind="fieldProps"
      :id="fieldId"
      :type="field.type"
      :value="modelValue"
      :disabled="disabled"
      :is-readonly="readonly"
      label=""
    />

    <!-- Date Field -->
    <DatePicker
      v-else-if="field.type === 'date'"
      class="!mb-0 w-full"
      v-bind="fieldProps"
      :id="fieldId"
      :value="modelValue"
      :disabled="disabled"
      :is-readonly="readonly"
      :required="field.required"
      label=""
    />

    <!-- Select Field -->
    <SelectField
      v-else-if="field.type === 'select'"
      class="!mb-0 w-full"
      v-bind="fieldProps"
      :id="fieldId"
      :value="modelValue"
      :options="field.options || []"
      :disabled="disabled"
      :is-readonly="readonly"
      :searchable="true"
      label=""
    />

    <!-- Checkbox Field -->
    <div v-else-if="field.type === 'checkbox'" class="flex items-center">
      <CheckboxField
        class="!mb-0"
        v-bind="fieldProps"
        :id="fieldId"
        :initially-checked="modelValue"
        :disabled="disabled"
        :checked-value="true"
        label=""
        @change="$emit('change', $event)"
      />
    </div>

    <!-- Error Message -->
    <div v-if="errorMessage" class="mt-1 text-sm text-red-500">
      {{ errorMessage }}
    </div>
  </Field>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { Field } from 'vee-validate';
  import {
    CheckboxField,
    DatePicker,
    SelectField,
    TextField,
  } from '@/components/form';
  import type { FormFieldConfig } from '../../types/form-config';

  interface Props {
    field: FormFieldConfig;
    modelValue: any;
    disabled?: boolean;
    readonly?: boolean;
    fieldName?: string;
    fieldId?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    disabled: false,
    readonly: false,
  });

  const fieldName = computed(() => props.fieldName || props.field.key);
  const fieldId = computed(() => props.fieldId || props.field.key);

  defineEmits<{
    change: [value: any];
  }>();
</script>
