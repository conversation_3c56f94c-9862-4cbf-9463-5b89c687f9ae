import { apiClient } from '@/services/api';
import { ResponseType } from '@/services/api/apiClient';
import { UnauthorizedError } from '@/services/api';
import { dtoToMessageMapper } from '../utils/mappers/dtoToMessageMapper';
import { AllFormValues, AssistantTextFormValues } from '../types/form-model';
import {
  UserTextData,
  User_Addresses_Data,
  User_PersonalDetails_Data,
  User_Relations_Data,
  WithdrawConsentData,
} from '../types/message';
import { Message } from '../types/message';
import { generate_placeholder_message_metadata } from '../utils/helpers';

export default async function* (
  body:
    | UserTextData
    | User_PersonalDetails_Data
    | User_Addresses_Data
    | User_Relations_Data
    | WithdrawConsentData,
): AsyncGenerator<AllFormValues, void, unknown> {
  let reader: ReadableStreamDefaultReader<Uint8Array>;

  try {
    const response: ReadableStream<Uint8Array> = await apiClient.post(
      `/api/v2/chatbot/message`,
      body,
      {
        responseType: ResponseType.Stream,
      },
    );

    reader = response.getReader();
  } catch (error) {
    // Let unauthorized errors (including chatbot permission errors) propagate to the store
    if (error instanceof UnauthorizedError) {
      throw error;
    }

    // Check if this is a 401 error in any form and re-throw it
    if (error && typeof error === 'object') {
      const errorObj = error as any;
      // Check various places where the 401 status might be stored
      if (
        errorObj.$metadata?.httpStatusCode === 401 ||
        errorObj._response?.statusCode === 401
      ) {
        throw error;
      }
    }

    // Backstop error displayed to user in the event the backend does not send a viable in-chat error message to the front end via the stream and instead sends an HTTP error. As this error message is not persisted in the backend it will disappear on page refresh
    const { id, timestamp } = generate_placeholder_message_metadata();

    yield {
      id: id,
      timestamp: timestamp,
      messageLayout: 'assistant_text_layout',
      message:
        'Unfortunately our server had an issue processing your message. Please try again in a moment.',
    } as AssistantTextFormValues;
    return;
  }

  const decoder = new TextDecoder();
  const delimiter = '\n---\n';
  let buffer = '';

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });

      let delimiterIndex: number;
      while ((delimiterIndex = buffer.indexOf(delimiter)) !== -1) {
        const chunk = buffer.slice(0, delimiterIndex);
        buffer = buffer.slice(delimiterIndex + delimiter.length);

        if (chunk.trim()) {
          const message = JSON.parse(chunk);
          yield dtoToMessageMapper(message);
        }
      }
    }

    if (buffer.trim()) {
      const msg = JSON.parse(buffer) as Message;
      yield dtoToMessageMapper(msg);
    }
  } catch {
    const { id, timestamp } = generate_placeholder_message_metadata();

    yield {
      id: id,
      timestamp: timestamp,
      messageLayout: 'assistant_text_layout',
      message:
        'Unfortunately our server ran into a problem whilst generating the response to your message. Please try again in a moment.',
    } as AssistantTextFormValues;
    return;
  }
}
