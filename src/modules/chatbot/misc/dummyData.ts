import { AllFormValues } from '../types/form-model';

export const MessagesTestData: AllFormValues[] = [
  {
    id: 1,
    timestamp: '2025-02-15T12:00:00Z',
    messageLayout: 'assistant_text_layout',
    message: "Hi there! I'm <PERSON><PERSON>ril, how can I help?",
  },
  {
    id: 2,
    timestamp: '2025-02-15T12:00:00Z',
    messageLayout: 'user__health_score_layout',
    overallScore: 72,
    goalScores: [
      {
        score: 72,
        type: 'goal',
        name: 'goal',
        error: 'error',
        cashflowTimeseries: [
          [
            new Date('2024-01-01'),
            new Date('2024-06-01'),
            new Date('2024-12-01'),
            new Date('2025-06-01'),
            new Date('2025-12-01'),
          ],
          [1000, 2500, 4000, 5500, 6414.81],
        ],
        targetAmount: 5500,
        targetAmountInflated: 5650,
        targetDate: new Date('2026-09-01'),
        holdingWarnings: [
          {
            productType: 'ISA',
            providerName: '<PERSON><PERSON><PERSON>',
            accountNumber: '**********',
            warning: 'This is a warning',
          },
          {
            productType: 'Pension',
            providerName: '<PERSON><PERSON><PERSON>e',
            accountNumber: '**********',
            warning: 'This is a warning for pension',
          },
        ],
      },
      {
        score: 80,
        type: 'goal',
        name: 'goal2',
        error: 'error2',
        cashflowTimeseries: [
          [
            new Date('2024-01-01'),
            new Date('2024-06-01'),
            new Date('2024-12-01'),
            new Date('2025-06-01'),
            new Date('2025-12-01'),
          ],
          [500, 1200, 2000, 3200, 4800],
        ],
        targetAmount: 4000,
        targetAmountInflated: 4500,
        targetDate: new Date('2025-12-01'),
        holdingWarnings: [
          {
            productType: 'Other',
            providerName: 'Hargreaves Lansdowne',
            accountNumber: '**********',
            warning: 'This is a warning',
          },
          {
            productType: 'Something Else',
            providerName: 'Hargreaves Lansdowne',
            accountNumber: '**********',
            warning: 'This is a warning for pension',
          },
        ],
      },
    ],
  },
  {
    id: 3,
    timestamp: '2025-02-16T12:00:00Z',
    messageLayout: 'user_text_layout',
    message: "I'd like to update my date of birth.",
  },
  {
    id: 4,
    timestamp: '2025-02-18T12:00:00Z',
    messageLayout: 'assistant_text_layout',
    message: 'Sure, please provide your date of birth in the field below.',
  },
  {
    id: 5,
    timestamp: '2025-02-18T12:00:00Z',
    messageLayout: 'assistant_text_layout',
    message: 'Thanks.',
  },
  {
    id: 6,
    timestamp: '2025-02-18T12:00:00Z',
    messageLayout: 'user__relations_layout',
    completed: false,
    availableFields: null,
    relations: [
      {
        id: null,
        firstName: 'John',
        lastName: 'Doe',
        relationshipType: 'Spouse',
        dateOfBirth: '1990-01-01',
      },
    ],
  },
  {
    id: 7,
    timestamp: '2025-02-18T12:00:00Z',
    messageLayout: 'user__addresses_layout',
    completed: false,
    availableFields: null,
    addresses: [
      {
        id: null,
        addressLineOne: 'ONE',
        addressLineTwo: 'line two',
        addressLineThree: 'line three',
        addressLineFour: 'line four',
        city: 'London',
        postCode: 'AS22ISS',
        countryId: 1,
        movedInDate: '2024-05-05',
        movedOutDate: '2024-05-05',
        isPrimary: false,
      },
      {
        id: null,
        addressLineOne: 'TWO',
        addressLineTwo: 'line two',
        addressLineThree: 'line three',
        addressLineFour: 'line four',
        city: 'London',
        postCode: 'AS22ISS',
        countryId: 1,
        movedInDate: '2024-05-05',
        movedOutDate: '2024-05-05',
        isPrimary: true,
      },
      {
        id: null,
        addressLineOne: 'THREE',
        addressLineTwo: 'line two',
        addressLineThree: 'line three',
        addressLineFour: 'line four',
        city: 'London',
        postCode: 'AS22ISS',
        countryId: 1,
        movedInDate: '2024-05-05',
        movedOutDate: '2024-05-05',
        isPrimary: false,
      },
    ],
  },
];
