import { SelectOption } from '@/components/form/fields/field-model';
import type { AnySchema } from 'yup';

export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
}

export interface FormFieldConfig {
  key: string;
  type: 'text' | 'email' | 'date' | 'select' | 'checkbox' | 'array';
  label: string;
  required: boolean;
  options?: SelectOption[];
  validation?: any;
  hidden?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  arrayItemSchema?: any; // For array fields, the schema of items in the array
}

export interface FormConfig {
  title: string;
  validationSchema: string;
}

export type FormSubmitHandler<T = any> = (
  formValues: T,
) => void | Promise<void>;

export interface FormMessage {
  id?: string | number;
  timestamp?: string;
  messageLayout: string;
  availableFields?: string[] | null;
  completed?: boolean;
  method?: HttpMethod;
  changesMade?: boolean;
  [key: string]: any;
}

export interface FieldOverride {
  type?: FormFieldConfig['type'];
  options?: SelectOption[];
  defaultValue?: any;
  label?: string;
}

export interface FieldHandler {
  onChange?: (value: any, index?: number) => void;
}

export interface FieldOverrides {
  [fieldKey: string]: FieldOverride;
}

export interface FieldHandlers {
  [fieldKey: string]: FieldHandler;
}

// Helper functions to introspect Yup schemas
export function getFieldsFromSchema(
  schema: AnySchema,
  availableFields: string[] | null = null,
): FormFieldConfig[] {
  const fields: FormFieldConfig[] = [];

  try {
    // Get the schema's fields
    const schemaFields = (schema as any).fields || {};

    Object.entries(schemaFields).forEach(
      ([key, fieldSchema]: [string, any]) => {
        // Skip if field is not in availableFields (when availableFields is specified)
        // Handle both camelCase and snake_case field names
        if (availableFields && availableFields.length > 0) {
          const snakeCaseKey = camelToSnakeCase(key);
          if (
            !availableFields.includes(key) &&
            !availableFields.includes(snakeCaseKey)
          ) {
            return;
          }
        }

        const fieldType = getFieldType(fieldSchema, key);

        // Skip fields that can't be auto-generated
        if (fieldType === null) {
          return;
        }

        const isRequired = isFieldRequired(fieldSchema);

        const fieldConfig: FormFieldConfig = {
          key,
          type: fieldType as FormFieldConfig['type'],
          label: formatFieldLabel(key),
          required: isRequired,
        };

        // For array fields, extract the schema of array items
        if (fieldType === 'array') {
          fieldConfig.arrayItemSchema = fieldSchema.innerType;
        }

        fields.push(fieldConfig);
      },
    );
  } catch (error) {
    console.warn('Error extracting fields from schema:', error);
  }

  return fields;
}

// Explicit field type mapping since Yup schema introspection doesn't preserve tests
const FIELD_TYPE_OVERRIDES: Record<string, FormFieldConfig['type']> = {
  // Personal details
  dateOfBirth: 'date',
  email: 'email',

  // Address fields
  movedInDate: 'date',
  movedOutDate: 'date',
  moved_in_date: 'date',
  moved_out_date: 'date',
};

function getFieldType(
  fieldSchema: any,
  fieldKey?: string,
): FormFieldConfig['type'] | 'array' | null {
  const type = fieldSchema?.type || fieldSchema?.spec?.type;

  // Check explicit overrides first
  if (fieldKey && FIELD_TYPE_OVERRIDES[fieldKey]) {
    return FIELD_TYPE_OVERRIDES[fieldKey];
  }

  // Return 'array' type for arrays instead of skipping them
  if (type === 'array') {
    return 'array';
  }

  switch (type) {
    case 'string':
      // Check if it's an email field based on validation
      if (fieldSchema?.tests?.some((test: any) => test.name === 'email')) {
        return 'email';
      }
      // Check if it's a date field based on validation or format
      if (
        fieldSchema?.tests?.some((test: any) => test.name === 'is-valid-date')
      ) {
        return 'date';
      }
      return 'text';
    case 'number':
      return 'text'; // Use text input with number validation
    case 'boolean':
      return 'checkbox';
    default:
      return 'text';
  }
}

function isFieldRequired(fieldSchema: any): boolean {
  try {
    // Check if the field has a 'required' test
    return (
      fieldSchema?.tests?.some((test: any) => test.name === 'required') || false
    );
  } catch {
    return false;
  }
}

function camelToSnakeCase(str: string): string {
  return str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);
}

function formatFieldLabel(fieldKey: string): string {
  // Convert camelCase to Title Case
  return fieldKey
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, (str) => str.toUpperCase())
    .trim();
}
