import { AccountModelFactor } from '@modules/accounts';
import { Nullable } from '@/types/Common';

export interface GoalAccount
  extends Pick<
    AccountModelFactor,
    | 'id'
    | 'providerName'
    | 'type'
    | 'typeGroupId'
    | 'accountNumber'
    | 'subAccountNumber'
    | 'advices'
    | 'status'
    | 'originalStatus'
    | 'feeSplitTemplate'
    | 'expectedFees'
    | 'clients'
  > {
  advisorId: Nullable<AccountModelFactor['advisor']['id']>;
}
