import { CaseClient } from '@modules/cases';
import { ClientId } from '@modules/clients';
import { GoalAccount } from '@modules/goals/models/goal-account';
import { ClientGoalTypeEnum } from '@modules/clients/models';
import { GoalId } from '../types';

export interface ModelFactor {
  id: GoalId;
  name: string;
  type: ClientGoalTypeEnum;
  clients: CaseClient[];
}

export type GoalObjectives = {
  clientId: ClientId;
  content: string;
};

// CaseGoal
export interface Goal extends ModelFactor {
  accounts: GoalAccount[];
  goalObjectives: GoalObjectives[];
}
