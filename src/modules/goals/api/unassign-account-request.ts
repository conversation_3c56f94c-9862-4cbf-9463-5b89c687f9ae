import { apiClient } from '@/services/api';
import { CaseId, TaskCustomCommand } from '@modules/cases';
import { TaskId } from '@modules/tasks';

type Command = TaskCustomCommand.RemoveAccountFromReview;

type Body = {
  account_id: number;
};

export default async (
  caseId: CaseId,
  taskId: TaskId,
  accountId: number,
): Promise<void> => {
  await apiClient.patch<{ command: Command; payload: Body }, Promise<void>>(
    `/api/v2/case/${caseId}/accounts_to_review/${taskId}`,
    {
      command: TaskCustomCommand.RemoveAccountFromReview,
      payload: { account_id: accountId },
    },
  );
};
