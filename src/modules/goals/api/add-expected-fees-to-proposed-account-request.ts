import { type GoalAccount, type GoalId } from '@modules/goals';
import { type ExpectedFeeDto } from '@modules/accounts/models';
import { apiClient } from '@/services/api';
import { CaseId, TaskCustomCommand } from '@modules/cases';
import { TaskId } from '@modules/tasks';
import { TaskCustomCommandEnum } from '@modules/cases/types/Commands';

type Command = TaskCustomCommand.UpdateAccountExpectedFees;

interface AccountPayload {
  accountId: GoalAccount['id'];
  expectedFees: Array<{
    id: number | undefined;
    initial: number;
    dueDate: string;
    amount: string;
  }>;
}

export const addExpectedFeesToProposedAccountRequest = async (
  caseId: CaseId,
  caseGoalId: GoalId,
  taskId: TaskId,
  accountId: GoalAccount['id'],
  expectedFees: Array<{
    id: number | undefined;
    initial: number;
    dueDate: string;
    amount: string;
  }>,
): Promise<void> => {
  const expected_fees: ExpectedFeeDto[] = expectedFees.map((fee) => ({
    id: fee.id,
    case_goal_id: caseGoalId,
    fee_type: fee.initial,
    due_date: fee.dueDate,
    amount: fee.amount,
  }));
  await apiClient.patch<
    {
      command: Command;
      payload: {
        account_id: AccountPayload['accountId'];
        expected_fees: ExpectedFeeDto[];
      };
    },
    Array<ExpectedFeeDto>
  >(`/api/v2/case/${caseId}/add_fee_est/${taskId}`, {
    command: TaskCustomCommandEnum.UpdateAccountExpectedFees,
    payload: { account_id: accountId, expected_fees },
  });
};
