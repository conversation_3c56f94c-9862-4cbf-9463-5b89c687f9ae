import { apiClient } from '@/services/api';
import { AdviceDTO } from '@modules/accounts/models/advice';
import { AccountDTO } from '@modules/accounts/dtos/account-dto';
import { CaseId, TaskCustomCommand } from '@modules/cases';
import { TaskId } from '@modules/tasks';

type Command = TaskCustomCommand.UpdateAdviceAcceptance;

type AdviceBody = {
  account_id: AccountDTO['id'];
  advice: Pick<AdviceDTO, 'id' | 'is_accepted'>[];
};

type Body = AdviceBody;

export const acceptAdviceRequest = async (
  caseId: CaseId,
  taskId: TaskId,
  body: AdviceBody,
): Promise<void> =>
  await apiClient.patch<{ command: Command; payload: Body }, Promise<void>>(
    `/api/v2/case/${caseId}/acceptance_from_client/${taskId}`,
    {
      command: TaskCustomCommand.UpdateAdviceAcceptance,
      payload: { ...body },
    },
  );
