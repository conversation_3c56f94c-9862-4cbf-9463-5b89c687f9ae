import { apiClient } from '@/services/api';
import { Nullable } from '@/types/Common';
import { GoalId } from '@modules/goals';
import { ClientId } from '@modules/clients';
import {
  CashflowItem,
  ForecasterData,
  ForecasterResults,
  ForecasterResultsDTO,
  RiskLevelType,
} from '@modules/goals';

type ForecasterResponseData = {
  inflation: Nullable<number>;
  risk: Nullable<RiskLevelType>;
  retirement_age: number;
  cashflows: CashflowItem[];
  comments: string;
  updated_at: string;
};

type ForecasterRequestData = {
  inflation: Nullable<number>;
  recommended_risk: RiskLevelType;
  selected_risk?: RiskLevelType;
  retirement_age: number;
  cashflows: CashflowItem[];
  comments?: Nullable<string>;
};

type Body = ForecasterRequestData & {
  client_id: ClientId;
  goal_id: number | null;
  persist: boolean;
};

export const generateForecasterPlan = async (
  goalId: GoalId,
  clientId: ClientId,
  persist = false,
  data: ForecasterRequestData,
): Promise<ForecasterResultsDTO> => {
  const response = await apiClient.post<Body, ForecasterResults>(
    `/api/v1/goals/cash-forecast`,
    {
      client_id: clientId,
      goal_id: persist ? goalId : null,
      persist,
      ...data,
    },
  );

  return {
    data: response.data,
    extras: response.extras?.loss_capacity || null,
  };
};

export const fetchForecasterPlan = async (
  goalId: GoalId,
  clientId: ClientId,
): Promise<ForecasterData | null> => {
  const data = await apiClient.get<ForecasterResponseData>(
    `/api/v1/goals/${goalId}/cash-forecast`,
    {
      client_id: clientId,
    },
  );

  return data
    ? {
        cashflows: data.cashflows,
        options: {
          retirementAge: data.retirement_age,
          inflationRate: data.inflation,
          inflationAdjusted: !!data.inflation,
          selectedRisk: data.risk ?? null,
        },
        comments: data.comments,
        updated_at: data.updated_at,
      }
    : null;
};
