import { Task } from './task';

export enum TaskTypeEnum {
  Default = 'Default',
  AccountsToReview = 'AccountsToReview',
  CreateAdvice = 'CreateAdvice',
  OngoingProviderChasing = 'OngoingProviderChasing',
  FinalCheck = 'FinalCheck',
  RiskProfile = 'RiskProfile',
  CashflowPlan = 'CashflowPlan',
  AcceptanceFromClient = 'AcceptanceFromClient',
  AdviceFeeEstimation = 'AdviceFeeEstimation',
  ImplementAccount = 'ImplementAccount',
  PlanningReportGenerated = 'PlanningReportGenerated',
  LetterOfAuthorityGenerated = 'LetterOfAuthorityGenerated',
  HeadedLetterGenerated = 'HeadedLetterGenerated',
  FinancialSummaryDocumentGenerated = 'FinancialSummaryDocumentGenerated',
  LoaCoverLetterGenerated = 'LoaCoverLetterGenerated',
  ClientAnnualReviewMessage = 'ClientAnnualReviewMessage',
  ClientOnboardingMessage = 'ClientOnboardingMessage',
}
export const isOfType = (task: Task, type: TaskTypeEnum): boolean =>
  task.type === type;

export const isDefaultTask = (task: Task) =>
  isOfType(task, TaskTypeEnum.Default);

export const isAccountToReviewTask = (task: Task) =>
  isOfType(task, TaskTypeEnum.AccountsToReview);

export const isCreateAdviceTask = (task: Task) =>
  isOfType(task, TaskTypeEnum.CreateAdvice);
export const isAcceptanceFromClientTask = (task: Task) =>
  isOfType(task, TaskTypeEnum.AcceptanceFromClient);

export const isFinalCheckAllAdviceImplemented = (task: Task) =>
  isOfType(task, TaskTypeEnum.FinalCheck);

export const isRiskProfileTask = (task: Task) =>
  isOfType(task, TaskTypeEnum.RiskProfile);

export const isCashflowPlanTask = (task: Task) =>
  isOfType(task, TaskTypeEnum.CashflowPlan);

export const isAdviceFeeEstimationTask = (task: Task) =>
  isOfType(task, TaskTypeEnum.AdviceFeeEstimation);

export const isPlanningReportGeneratedTask = (task: Task) =>
  isOfType(task, TaskTypeEnum.PlanningReportGenerated);

export const isLetterOfAuthorityGeneratedTask = (task: Task) =>
  isOfType(task, TaskTypeEnum.LetterOfAuthorityGenerated);

export const isHeadedLetterGeneratedTask = (task: Task) =>
  isOfType(task, TaskTypeEnum.HeadedLetterGenerated);

export const isFinancialSummaryDocumentGeneratedTask = (task: Task) =>
  isOfType(task, TaskTypeEnum.FinancialSummaryDocumentGenerated);

export const isLetterOfAuthorityCoverLetterGeneratedTask = (task: Task) =>
  isOfType(task, TaskTypeEnum.LoaCoverLetterGenerated);

export const isClientMessageTask = (task: Task) =>
  isOfType(task, TaskTypeEnum.ClientAnnualReviewMessage) ||
  isOfType(task, TaskTypeEnum.ClientOnboardingMessage);
