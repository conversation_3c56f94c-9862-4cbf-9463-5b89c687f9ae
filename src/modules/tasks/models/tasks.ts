import { IDateTime } from '@/utils/dateTime';
import { Advisor } from '@modules/advisors';
import { TaskStatus } from './task-status';
import { CaseType } from '@modules/cases/models';

export interface TaskListItem {
  id: number;
  slug: string;
  description: string;
  assignee: Pick<Advisor, 'id' | 'firstName' | 'lastName'> | null;
  assigneeGroup: string | null;
  caseId: number;
  clientId: number;
  caseName: string;
  goalName: string | null;
  goalId: number | null;
  status: TaskStatus;
  dueDate: IDateTime | null;
  caseType: CaseType;
}

export interface TaskList {
  tasks: TaskListItem[];
  totalTasks: number;
}
