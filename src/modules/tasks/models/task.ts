import { Advisor, AdvisorRoleEnum } from '@modules/advisors';
import { IDateTime } from '@/utils/dateTime';
import { Advice, IAccountType } from '@modules/accounts/models';
import { TaskId } from '../types/TaskId';
import { TaskTypeEnum } from './task-type';
import { type ITaskStatus, TaskStatusEnum } from './task-status';
import { NonNullableFields, Nullable } from '@/types/Common';
import { SubTask } from '@modules/cases/models';
import { CaseGoal } from '@modules/cases/models/case–goal';

interface Account {
  id: number;
  provider: string;
  accountType: IAccountType;
  accountNumber: string;
  subAccountNumber: string;
}

// deprecated
// eslint-disable-next-line
interface ExistingAccount extends Account {
  advices: Advice[];
}

// deprecated
// eslint-disable-next-line
interface ProposedAccount extends Account {
  id: number;
  provider: string;
  accountType: IAccountType;
  accountNumber: string;
  subAccountNumber: string;
}

export interface ModelFactor {
  id: Nullable<TaskId>;
  slug: string;
  type: TaskTypeEnum;
  caseLevel: boolean;
  description: string;
  process_description: string | null;
  advisor: Pick<Advisor, 'id' | 'lastName' | 'firstName'> | null;
  status: ITaskStatus;
  dueDate: IDateTime;
  createdOn: IDateTime;
  isModifiable: boolean;
  defaultGroup: AdvisorRoleEnum;
  assignedGroup: AdvisorRoleEnum | null;
}

export interface Task
  extends Pick<
    ModelFactor,
    | 'slug'
    | 'type'
    | 'status'
    | 'caseLevel'
    | 'advisor'
    | 'description'
    | 'process_description'
    | 'defaultGroup'
    | 'dueDate'
    | 'isModifiable'
    | 'assignedGroup'
  > {
  subTasks: SubTask[];
}

export type TaskGoal = { taskSlug: Task['slug'] } & CaseGoal & SubTask;
export type TaskGoalRequired = { taskSlug: Task['slug'] } & CaseGoal &
  NonNullableFields<SubTask>;

export const isTaskCompleted = (task: Task) =>
  task.status?.toType() === TaskStatusEnum.Completed;

export const isTaskCanceled = (task: Task) =>
  task.status?.toType() === TaskStatusEnum.Canceled;

export const isTaskNotApplicable = (task: Task) =>
  task.status?.toType() === TaskStatusEnum.NotApplicable;

export const isTaskOpen = (task: Task) =>
  task.status?.toType() === TaskStatusEnum.InProgress;

export const isTaskFinished = (task: Task) =>
  isTaskCanceled(task) || isTaskCompleted(task) || isTaskNotApplicable(task);

export const isTaskInReview = (task: Task) =>
  task.status?.toType() === TaskStatusEnum.Review;
