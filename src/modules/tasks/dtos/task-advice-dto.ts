import { Advice } from '@modules/accounts/models';

export interface TaskAdviceDTO {
  advice_holding_type_id: number;
  advice_note: string;
  advice_taken: boolean;
  advice_type_id: number;
  case_goal_id: number;
  holding_id: number;
  id: number;
  is_implemented: boolean;
  is_accepted: boolean;
}

export const dtoToDomain = (dto: TaskAdviceDTO): Advice => ({
  id: dto.id,
  type: dto.advice_type_id,
  description: dto.advice_note,
  isImplemented: dto.is_implemented,
  isAccepted: dto.is_accepted,
});
