export enum CaseCommandEnum {
  CompleteCase = 'completeCase',
  CancelCase = 'cancelCase',
  UpdateCaseAdviser = 'updateCaseAdviser',
  UpdateCaseManager = 'updateCaseManager',
  UpdateCaseReviewSlot = 'updateCaseReviewSlot',
  AddCaseNote = 'addNote',
}

export enum TaskSlugCommandEnum {
  UpdateTaskAssigneeGroup = 'updateTaskAssigneeGroup',
  UpdateTaskAssignee = 'updateTaskAssignee',
  UpdateTaskDueDate = 'updateTaskDueDate',
  ChangeTaskStatus = 'changeTaskStatus',
}

export enum TaskCustomCommandEnum {
  AddAccountsToReview = 'addAccountsToReview',
  RemoveAccountFromReview = 'removeAccountFromReview',
  AddProposedAccount = 'addProposedAccount',
  RemoveProposedAccount = 'removeProposedAccount',
  UpdateAccountAdvice = 'updateAccountAdvice',
  UpdateAccountExpectedFees = 'updateAccountExpectedFees',
  UpdateAdviceAcceptance = 'updateAdviceAcceptance',
  UpdateAdviceImplementation = 'updateAdviceImplementation',
  RequestDocument = 'requestDocument',
  ClientMessage = 'sendClientEmail',
  UpdateDocumentsRequirement = 'updateDocumentsRequirement',
}
