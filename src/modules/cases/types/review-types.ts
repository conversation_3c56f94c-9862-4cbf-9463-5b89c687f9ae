export interface IReviewType {
  toLabel: () => string;
  toValue: () => ReviewTypeEnum;
}

export enum ReviewTypeEnum {
  AnnualReview = 2,
  Catchup = 5,
}

const reviewTypeMap = new Map<ReviewTypeEnum, string>([
  [ReviewTypeEnum.AnnualReview, 'Annual Review'],
  [ReviewTypeEnum.Catchup, 'Catch-up'],
]);

export class ReviewType implements IReviewType {
  constructor(private readonly value: ReviewTypeEnum) {}

  toLabel(): string {
    return reviewTypeMap.get(this.value) as string;
  }

  toValue(): ReviewTypeEnum {
    return this.value as ReviewTypeEnum;
  }
}
