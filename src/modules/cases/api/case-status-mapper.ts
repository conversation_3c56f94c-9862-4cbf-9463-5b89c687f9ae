import { CaseStatus } from '../models';
import { CaseStatusEnum } from '../models/case-status';
import { CaseStatusDto, CaseStatusDtoEnum } from '../dtos/case-status-dto';

export const fromDTO = (status: CaseStatusDto): CaseStatus => {
  const map = new Map<CaseStatusDto, CaseStatus>([
    [CaseStatusDtoEnum.Open, CaseStatusEnum.Open],
    [CaseStatusDtoEnum.Completed, CaseStatusEnum.Completed],
    [CaseStatusDtoEnum.Cancelled, CaseStatusEnum.Cancelled],
  ]);
  return map.get(status) as CaseStatus;
};

export const toDTO = (status: CaseStatus): CaseStatusDto => {
  const map = new Map<CaseStatusEnum, CaseStatusDto>([
    [CaseStatusEnum.Open, CaseStatusDtoEnum.Open],
    [CaseStatusEnum.Completed, CaseStatusDtoEnum.Completed],
    [CaseStatusEnum.Cancelled, CaseStatusDtoEnum.Cancelled],
  ]);
  return map.get(status) as CaseStatusDto;
};
