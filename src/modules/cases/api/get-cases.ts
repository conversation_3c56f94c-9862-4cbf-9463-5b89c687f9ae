import { array, number, object, string } from 'yup';
import { apiClient } from '@/services/api';
import { QueryParams } from '@/types/api';
import { DateTime } from '@/utils/dateTime';
import { GoalId } from '@modules/goals';
import { CaseType } from '@modules/cases/models';
import { ClientId } from '@modules/clients';
import { CaseId } from '@modules/cases';
import { CaseList } from '../models/cases';
import { fromDTO as statusFromDTO } from './case-status-mapper';

const getGoalsValidationSchema = object({
  data: array()
    .of(
      object({
        id: number().required(),
        case_type: number().required(),
        name: string().required(),
        status: number().required(),
        adviser: object({
          id: number().required(),
          first_name: string().required(),
          last_name: string().defined(),
        }).required(),
        clients: array()
          .of(
            object({
              id: number().required(),
              first_name: string().required(),
              last_name: string().defined(),
              // TODO: change to defined() once AV-911 is enabled
              email: string().nullable().optional(),
            }).required(),
          )
          .required(),
        goals: array()
          .of(
            object({
              id: number().required(),
              goal_id: number().required(),
              goal_name: string().defined(),
              // TODO: change to defined() once AV-911 is enabled
              email: string().nullable().optional(),
            }).required(),
          )
          .required(),
        tasks_complete: number().required(),
        tasks_total: number().required(),
        due_date: string().nullable().defined(),
      }),
    )
    .required(),
  count: number().required(),
});

export default async (queryParams?: QueryParams): Promise<CaseList> => {
  const response = await apiClient.get<Promise<unknown>>(
    '/api/v2/case/',
    queryParams,
  );

  const casesDTO = await getGoalsValidationSchema.validate(response);

  const cases = casesDTO.data.map((caseDTO) => ({
    id: caseDTO.id as CaseId,
    caseName: caseDTO.name,
    caseType: new CaseType(caseDTO.case_type),
    status: statusFromDTO(caseDTO.status),
    adviser: {
      id: caseDTO.adviser.id,
      firstName: caseDTO.adviser.first_name,
      lastName: caseDTO.adviser.last_name,
    },
    clients: caseDTO.clients.map((client) => ({
      id: client.id as ClientId,
      firstName: client.first_name,
      lastName: client.last_name,
      email: client.email,
    })),
    goals: caseDTO.goals.map((goal) => ({
      id: goal.id as GoalId,
      goalId: goal.goal_id,
      goalName: goal.goal_name,
    })),
    tasksCompleted: caseDTO.tasks_complete,
    taskTotal: caseDTO.tasks_total,
    dueDate: new DateTime(caseDTO.due_date),
  }));

  return {
    cases,
    totalCases: casesDTO.count,
  };
};
