import { Mailable } from '@/types/Mailable';
import { CaseId } from '@modules/cases';
import { ClientId } from '@modules/clients';
import { IReviewType } from '@modules/cases/types/review-types';

interface ReviewClient extends Mailable {
  id: ClientId;
  firstName: string;
  lastName: string;
}

interface ReviewCase {
  id: CaseId;
  adviserId: number;
  status: number;
}

export interface UpcomingReview {
  id: number;
  clients: ReviewClient[];
  clientOwnerId: number;
  reviewMonth: number;
  reviewYear: number;
  reviewType: IReviewType;
  caseData: ReviewCase | null;
}

export interface UpcomingReviewsResponse {
  totalReviews: number;
  upcomingReviews: UpcomingReview[];
}
