import { apiClient } from '@/services/api';
import { QueryParams } from '@/types/api';
import { ReviewSlot } from '@modules/upcoming-reviews';

interface Response {
  available_review_slots: Array<{
    id: number;
    description: string;
  }>;
  has_review_slots_outside_of_max_assignable_period: boolean;
}

export default async (
  queryParams?: QueryParams,
): Promise<{
  availableReviewSlots: ReviewSlot[];
  hasReviewSlotsOutsideOfMaxAssignablePeriod: boolean;
}> => {
  const response = await apiClient.get<Promise<Response>>(
    `/api/v1/review-group/available-review-slots`,
    queryParams,
  );

  return {
    availableReviewSlots: response.available_review_slots,
    hasReviewSlotsOutsideOfMaxAssignablePeriod:
      response.has_review_slots_outside_of_max_assignable_period,
  };
};
