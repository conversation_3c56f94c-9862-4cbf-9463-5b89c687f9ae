import { apiClient } from '@/services/api';
import { QueryParams } from '@/types/api';
import { CaseId } from '@modules/cases';
import { ClientId } from '@modules/clients';
import { UpcomingReviewsResponse } from '../models/upcoming-reviews';
import { UpcomingReviewsResponseDTO } from '../dtos/upcoming-review-dtos';
import { ReviewType } from '@modules/cases/types/review-types';

export default async (
  queryParams?: QueryParams,
): Promise<UpcomingReviewsResponse> => {
  const response = await apiClient.get<Promise<UpcomingReviewsResponseDTO>>(
    '/api/v1/case/upcoming-reviews',
    queryParams,
  );

  const upcomingReviewsDTO = response.upcoming_reviews.map((reviewDTO) => ({
    id: reviewDTO.id,
    clients: reviewDTO.clients.map((clientDTO) => ({
      id: clientDTO.id as ClientId,
      firstName: clientDTO.first_name,
      lastName: clientDTO.last_name,
      email: clientDTO.email || null,
    })),
    clientOwnerId: reviewDTO.client_owner_id,
    reviewMonth: reviewDTO.review_month,
    reviewYear: reviewDTO.review_year,
    reviewType: new ReviewType(reviewDTO.review_type),
    caseData: reviewDTO.case_data
      ? {
          id: reviewDTO.case_data.id as CaseId,
          adviserId: reviewDTO.case_data.adviser_id,
          status: reviewDTO.case_data.status,
        }
      : null,
  }));
  return {
    totalReviews: response.total_reviews,
    upcomingReviews: upcomingReviewsDTO,
  };
};
