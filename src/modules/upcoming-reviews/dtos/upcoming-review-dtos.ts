import { Mailable } from '@/types/Mailable';

interface ReviewClientDTO extends Mailable {
  id: number;
  first_name: string;
  last_name: string;
}

interface ReviewCaseDTO {
  id: number;
  adviser_id: number;
  status: number;
}

export interface UpcomingReviewDTO {
  id: number;
  clients: ReviewClientDTO[];
  client_owner_id: number;
  review_month: number;
  review_year: number;
  review_type: number;
  case_data: ReviewCaseDTO | null;
}

export interface UpcomingReviewsResponseDTO {
  total_reviews: number;
  upcoming_reviews: UpcomingReviewDTO[];
}
