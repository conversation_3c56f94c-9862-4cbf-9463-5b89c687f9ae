import { IDateTime } from '@/utils/dateTime';
import { Paginable } from '@/types/Pagination';
import { IMoney } from '@/utils/money';
import { IStatementStatus } from './statement-status';
import { IPaymentType } from './payment-type';
import { AccountId } from '@modules/accounts';
import { StatementStatusEnum } from '@modules/fees/models/statement-status';
import { ClientId } from '@modules/clients';

export interface ModelFactor {
  id: number;
  date: IDateTime;
  name: string;
  amount: IMoney;
  linesCountTotal: number;
  linesCountPaid: number;
  linesCountUnpaid: number;
  status: IStatementStatus;
  paymentType: IPaymentType;
  paymentDate: IDateTime;
  clientAccount: AccountId;
  clientId: ClientId;
  providerId: number;
  counterpartyType: 'PROVIDER' | 'CLIENT';
}

export interface StatementList extends Paginable<StatementListItem> {}

export interface StatementListItem
  extends Pick<
    ModelFactor,
    | 'id'
    | 'amount'
    | 'date'
    | 'name'
    | 'status'
    | 'linesCountTotal'
    | 'linesCountPaid'
    | 'linesCountUnpaid'
    | 'counterpartyType'
  > {}

export type StatementProviderId = number;

export interface ClientStatement
  extends Pick<
    ModelFactor,
    | 'id'
    | 'clientAccount'
    | 'clientId'
    | 'paymentDate'
    | 'paymentType'
    | 'amount'
    | 'counterpartyType'
    | 'status'
  > {
  counterpartyType: 'CLIENT';
}

export interface ProviderStatement
  extends Pick<
    ModelFactor,
    | 'id'
    | 'name'
    | 'paymentDate'
    | 'counterpartyType'
    | 'providerId'
    | 'amount'
    | 'status'
  > {
  counterpartyType: 'PROVIDER';
  lines: Array<{
    id: number;
    clientId: ClientId;
    clientName: string;
    clientAccountId: number;
    clientAccountNumber: string;
    clientAccountProviderName: string;
    amount: IMoney;
    paymentType: IPaymentType;
  }>;
}

export const canBeEdited = (statement: { status: IStatementStatus }) => {
  return statement.status.is(StatementStatusEnum.Unmatched);
};

export const canBeDeleted = (statement: { status: IStatementStatus }) => {
  return statement.status.is(StatementStatusEnum.Unmatched);
};

export const canBeViewed = (statement: { status: IStatementStatus }) => {
  return !statement.status.is(StatementStatusEnum.Unmatched);
};
