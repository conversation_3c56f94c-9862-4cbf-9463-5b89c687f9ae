export interface FeeDTO {
  id: number;
  account_no: string;
  client_first_name: string;
  client_last_name: string;
  counterparty_type: 'CLIENT' | 'PROVIDER';
  gross_amount: number;
  fee_receiver_name: string;
  administrator_name: string;
  paid_to_role: string;
  provider_name: string;
  type: string;
  split_type: string;
  split_percentage: number;
  split_value: number;
  is_paid: boolean;
  created_date: string;
  paid_date: string;
}
