import { formatName } from '@/utils/user';

type ClientFee = {
  counterparty_type: 'CLIENT';
  client_first_name: string;
  client_last_name: string;
};

type ProviderFee = {
  counterparty_type: 'PROVIDER';
  provider_name: string;
};

type Fee = ClientFee | ProviderFee;

const clientCounterpartyName = (obj: {
  client_first_name: string;
  client_last_name: string;
}) =>
  formatName({
    firstName: obj.client_first_name,
    lastName: obj.client_last_name,
  });

const providerCounterpartyName = (obj: { provider_name: string }) =>
  obj.provider_name;

export const inferCounterpartyName = (fee: Partial<Fee>) => {
  return fee.counterparty_type === 'CLIENT'
    ? clientCounterpartyName(fee as ClientFee)
    : providerCounterpartyName(fee as ProviderFee);
};
