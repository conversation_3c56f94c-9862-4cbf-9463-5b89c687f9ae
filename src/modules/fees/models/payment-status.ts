export interface IPaymentStatus {
  toLabel: () => string;
  toValue: () => PaymentStatusEnum;
  is: (status: PaymentStatusEnum) => boolean;
}

export enum PaymentStatusEnum {
  All = 'All',
  Matched = 'Matched',
  Unmatched = 'Unmatched',
  MatchedAndUnpaid = 'MatchedAndUnpaid',
  MatchedAndFullyPaid = 'MatchedAndFullyPaid',
  MatchedPartPaid = 'MatchedPartPaid',
}

export const paymentStatusMap = new Map<PaymentStatusEnum, string>([
  [PaymentStatusEnum.All, 'All'],
  [PaymentStatusEnum.Matched, 'Matched'],
  [PaymentStatusEnum.Unmatched, 'Unmatched'],
  [PaymentStatusEnum.MatchedAndFullyPaid, 'Matched & Paid'],
  [PaymentStatusEnum.MatchedAndUnpaid, 'Matched & Unpaid'],
]);

export class PaymentStatus implements IPaymentStatus {
  constructor(private readonly status: PaymentStatusEnum) {}

  toLabel(): string {
    return paymentStatusMap.get(this.status) as string;
  }

  toValue(): PaymentStatusEnum {
    return this.status;
  }

  is(status: PaymentStatusEnum): boolean {
    return this.status === status;
  }
}

export function factory(status: PaymentStatusEnum): IPaymentStatus {
  return new PaymentStatus(status);
}
