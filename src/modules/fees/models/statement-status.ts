export interface IStatementStatus {
  toLabel: () => string;
  toValue: () => StatementStatusEnum;
  is: (status: StatementStatusEnum) => boolean;
}

export enum StatementStatusEnum {
  All = 'All',
  Matched = 'Matched',
  Unmatched = 'Unmatched',
  MatchedAndUnpaid = 'MatchedAndUnpaid',
  PartPaid = 'PartPaid',
  MatchedAndFullyPaid = 'MatchedAndFullyPaid',
}

export const statementStatusMap = new Map<StatementStatusEnum, string>([
  [StatementStatusEnum.All, 'All'],
  [StatementStatusEnum.Matched, 'Matched'],
  [StatementStatusEnum.Unmatched, 'Unmatched'],
  [StatementStatusEnum.MatchedAndUnpaid, 'Matched & Unpaid'],
  [StatementStatusEnum.PartPaid, 'Part Paid'],
  [StatementStatusEnum.MatchedAndFullyPaid, 'Matched & Fully Paid'],
]);

export class StatementStatus implements IStatementStatus {
  constructor(private readonly status: StatementStatusEnum) {}

  toLabel(): string {
    return statementStatusMap.get(this.status) as string;
  }

  toValue(): StatementStatusEnum {
    return this.status;
  }

  is(status: StatementStatusEnum): boolean {
    return this.status === status;
  }
}

export function factory(status: StatementStatusEnum): IStatementStatus {
  return new StatementStatus(status);
}
