export interface MatchDTO {
  amount: number;
  client_first_name: string;
  client_last_name: string;
  counterparty_id: number;
  counterparty_type: string;
  provider_name: string;
  payment_id: number;
  payment_date: string;
  statement_id: number;
  statement_date: string;
}

interface MultiMatch {
  payments: { id: number }[];
  statements: { id: number }[];
}

export interface GetMatchesDTO {
  match_lines: Array<MatchDTO>;
  multimatches: Array<MultiMatch>;
}
