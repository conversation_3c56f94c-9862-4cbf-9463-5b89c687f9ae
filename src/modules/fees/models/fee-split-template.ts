import { Paginable } from '@/types/Pagination';

export type FeeSplitTemplateRole =
  | 'Adviser'
  | 'Introducer'
  | 'RelationshipManager'
  | 'Aventur';

export type FeeSplitTemplateType = 'Standard_Fee' | 'Bonus' | 'Aventur';

interface FeeSplitTemplateModelFactor {
  id: number;
  name: string;
  isActive: boolean;
  adviserId: number;
  adviserName: string;
  linkedAccounts: number;
  lines: Array<{
    name: number;
    role: FeeSplitTemplateRole;
    type: FeeSplitTemplateType;
    splitInitial: string;
    splitOngoing: string;
    payable: boolean;
  }>;
}

export type FeeSplitTemplateRefData = Pick<
  FeeSplitTemplateModelFactor,
  'id' | 'name' | 'isActive'
>;

export type FeeSplitTemplateListItem = Pick<
  FeeSplitTemplateModelFactor,
  'id' | 'name' | 'adviserName' | 'isActive' | 'linkedAccounts' | 'adviserId'
>;

export interface FeeSplitTemplateList
  extends Paginable<FeeSplitTemplateListItem> {}

export type FeeSplitTemplateDetails = Pick<
  FeeSplitTemplateModelFactor,
  'id' | 'name' | 'isActive' | 'adviserId' | 'lines'
>;
