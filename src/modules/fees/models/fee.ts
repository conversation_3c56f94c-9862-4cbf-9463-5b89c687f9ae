import { IDateTime } from '@/utils/dateTime';
import { Paginable } from '@/types/Pagination';
import { IMoney } from '@/utils/money';
import { IPercentage } from '@/utils/percentage/percentage';

export interface ModelFactor {
  id: number;
  accountNumber: string;
  name: string;
  gross: IMoney;
  feeReceiverName: string;
  adviserName: string;
  role: string;
  type: string;
  feeType: string;
  splitPercentage: IPercentage;
  splitValue: IMoney;
  isPaid: boolean;
  createdDate: IDateTime;
  paidDate: IDateTime;
}

export interface FeeList extends Paginable<FeeListItem> {}

export interface FeeListItem
  extends Pick<
    ModelFactor,
    | 'id'
    | 'accountNumber'
    | 'name'
    | 'gross'
    | 'feeReceiverName'
    | 'adviserName'
    | 'role'
    | 'type'
    | 'feeType'
    | 'splitPercentage'
    | 'splitValue'
    | 'isPaid'
    | 'createdDate'
    | 'paidDate'
  > {}
