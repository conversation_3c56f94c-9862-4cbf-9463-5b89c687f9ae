import { apiClient } from '@/services/api';

interface Body {
  template_name: string;
  is_active: boolean;
  administrator_id: number;
  lines: Array<{
    administrator_id: number;
    role: string;
    type: string;
    split_initial: string;
    split_ongoing: string;
    is_payable: boolean;
  }>;
}

export default async (body: Body) => {
  await apiClient.post<Body, Promise<void>>(
    '/api/v1/fee-split-templates',
    body,
  );
};
