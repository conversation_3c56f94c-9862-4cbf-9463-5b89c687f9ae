import { apiClient } from '@/services/api';
import { DateTime } from '@/utils/dateTime';
import { Money } from '@/utils/money';
import { ClientId } from '@modules/clients';
import { ProviderStatement } from '@modules/fees/models/statement';
import {
  StatementStatus,
  StatementStatusEnum,
} from '@modules/fees/models/statement-status';
import { dtoPaymentTypeToDomain } from '@modules/fees/models/statement-dto';

interface DTO {
  id: number;
  payment_date: string;
  provider_name: string;
  provider_id: number;
  amount: number;
  status: StatementStatusEnum;
  statement_lines: Array<{
    id: number;
    client_id: number;
    client_account_id: number;
    client_account_number: string;
    client_account_provider_name: string;
    client_name: string;
    amount: number;
    fee_type_id: number;
  }>;
}

export default async (providerId: number): Promise<ProviderStatement> => {
  const dto = await apiClient.get<DTO>(
    `/api/v1/fees/provider-statements/${providerId}`,
  );

  return {
    counterpartyType: 'PROVIDER',
    id: dto.id,
    name: dto.provider_name,
    paymentDate: new DateTime(dto.payment_date),
    amount: new Money(dto.amount),
    providerId: dto.provider_id,
    status: new StatementStatus(dto.status),
    lines: dto.statement_lines.map((lineDto) => ({
      id: lineDto.id,
      clientId: lineDto.client_id as ClientId,
      amount: new Money(lineDto.amount),
      clientName: lineDto.client_name,
      clientAccountId: lineDto.client_account_id,
      clientAccountNumber: lineDto.client_account_number,
      clientAccountProviderName: lineDto.client_account_provider_name,
      paymentType: dtoPaymentTypeToDomain(lineDto.fee_type_id),
    })),
  };
};
