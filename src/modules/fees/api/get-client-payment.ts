import { apiClient } from '@/services/api';
import { DateTime } from '@/utils/dateTime';
import { Money } from '@/utils/money';
import { ClientId } from '@modules/clients';
import type { ClientPayment } from '@modules/fees/models/payment';
import {
  PaymentDTO,
  dtoStatusToDomain,
} from '@modules/fees/models/payment-dto';

interface DTO {
  id: number;
  name: string;
  client_last_name: string;
  payment_date: string;
  amount: string;
  notes: string;
  client_id: number;
  status: PaymentDTO['status'];
}

export default async (paymentId: number): Promise<ClientPayment> => {
  const dto = await apiClient.get<DTO>(
    `/api/v1/fees/client-payments/${paymentId}`,
  );

  return {
    amount: new Money(+dto.amount),
    date: new DateTime(dto.payment_date),
    id: dto.id,
    name: dto.name,
    notes: dto.notes,
    clientId: dto.client_id as ClientId,
    counterpartyType: 'CLIENT',
    status: dtoStatusToDomain(dto.status),
  };
};
