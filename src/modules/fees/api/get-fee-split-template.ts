import { apiClient } from '@/services/api';
import { array, boolean, mixed, number, object, string } from 'yup';
import {
  type FeeSplitTemplateDetails,
  type FeeSplitTemplateRole,
  type FeeSplitTemplateType,
} from '@modules/fees/models/fee-split-template';
import { percentageDTOtoStr } from '@modules/fees/dtos/fee-split-template';

const getFeeSplitTemplateValidationSchema = object({
  id: number().required(),
  template_name: string().required(),
  administrator_id: number().required(),
  is_active: boolean().required().defined(),
  lines: array()
    .of(
      object({
        administrator_id: number().required(),
        role_id: mixed<FeeSplitTemplateRole>()
          .oneOf(['Adviser', 'Aventur', 'Introducer', 'RelationshipManager'])
          .required(),
        type_id: mixed<FeeSplitTemplateType>()
          .oneOf(['Aventur', 'Bonus', 'Standard_Fee'])
          .required(),
        split_initial: number().required(),
        split_ongoing: number().required(),
        is_payable: boolean().required(),
      }).required(),
    )
    .required(),
}).required();

export default async (
  feeSplitTemplateId: number,
): Promise<FeeSplitTemplateDetails> => {
  const response = await apiClient.get<Promise<unknown>>(
    `/api/v1/fee-split-templates/${feeSplitTemplateId}`,
  );

  const feeSplitTemplateDTO =
    await getFeeSplitTemplateValidationSchema.validate(response);

  return {
    id: feeSplitTemplateDTO.id,
    adviserId: feeSplitTemplateDTO.administrator_id,
    isActive: feeSplitTemplateDTO.is_active,
    name: feeSplitTemplateDTO.template_name,
    lines: feeSplitTemplateDTO.lines.map((line) => ({
      name: line.administrator_id,
      payable: line.is_payable,
      role: line.role_id,
      splitInitial: percentageDTOtoStr(line.split_initial),
      splitOngoing: percentageDTOtoStr(line.split_ongoing),
      type: line.type_id,
    })),
  };
};
