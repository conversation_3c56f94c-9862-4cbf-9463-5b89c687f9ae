import { apiClient } from '@/services/api';
import { DateTime } from '@/utils/dateTime';
import { Money } from '@/utils/money';
import { ClientId } from '@modules/clients';
import { ClientStatement } from '@modules/fees/models/statement';
import {
  StatementStatus,
  StatementStatusEnum,
} from '@modules/fees/models/statement-status';
import { dtoPaymentTypeToDomain } from '@modules/fees/models/statement-dto';

interface DTO {
  id: number;
  payment_date: string;
  fee_type_id: number;
  client_id: number;
  client_account_id: number;
  amount: number;
  status: StatementStatusEnum;
}

export default async (statementId: number): Promise<ClientStatement> => {
  const dto = await apiClient.get<DTO>(
    `/api/v1/fees/client-statements/${statementId}`,
  );

  return {
    counterpartyType: 'CLIENT',
    id: dto.id,
    clientId: dto.client_id as ClientId,
    amount: new Money(dto.amount),
    clientAccount: dto.client_account_id,
    paymentDate: new DateTime(dto.payment_date),
    paymentType: dtoPaymentTypeToDomain(dto.fee_type_id),
    status: new StatementStatus(dto.status),
  };
};
