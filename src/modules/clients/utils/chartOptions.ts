import { merge } from 'lodash';
import { ChartOptions } from 'chart.js';
import { Money, formatWithCurrency } from '@/utils/money';
import { SelectedRange } from '@modules/clients/types/SelectedRange';

const formatYScales = (value: string | number): string => {
  const _value = Number(value);

  if (_value >= 1000000) return `${Number(_value) / 1000000}m`;
  else if (_value >= 1000) return `${Number(_value) / 1000}k`;
  else if (_value <= -1000000) return `${Number(_value) / 1000000}m`;
  else if (_value <= -1000) return `${Number(_value) / 1000}k`;
  return _value.toString();
};

const formatTooltipLabel = (value: string): string => {
  const _value = new Money(Number(value.replaceAll(',', '')));
  return formatWithCurrency(_value);
};

export const getChartOptions = (
  selectedRange: SelectedRange,
  options: Partial<ChartOptions<'line'>> = {},
): ChartOptions<'line'> =>
  merge(
    {
      maintainAspectRatio: false,
      responsive: true,
      elements: {
        point: {
          radius: selectedRange === '1m' ? 2 : 3,
          pointStyle: 'rectRot',
        },
      },
      plugins: {
        legend: {
          display: false,
        },
        tooltip: {
          callbacks: {
            label: (tooltipItem) => {
              const value = tooltipItem.formattedValue;
              return formatTooltipLabel(value);
            },
          },
        },
      },
      scales: {
        // eslint-disable-next-line id-length
        y: {
          grid: {
            display: false,
          },
          border: {
            display: false,
          },
          position: 'right',
          ticks: {
            callback: (value) => formatYScales(value),
          },
        },
        // eslint-disable-next-line id-length
        x: {
          ticks: {
            source: 'data',
          },
          type: 'time',
          time: {
            tooltipFormat: 'd MMM yy',
            displayFormats: {
              day: 'd MMM yy',
              month: 'MMM yy',
              year: 'yyyy',
            },
            round: selectedRange === '1m' ? 'day' : 'month',
          },
        },
      },
    },
    options,
  );
