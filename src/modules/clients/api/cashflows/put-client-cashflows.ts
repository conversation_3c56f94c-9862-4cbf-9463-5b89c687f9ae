import { apiClient } from '@/services/api';
import { ClientId } from '@modules/clients';

interface Payload {
  type_id: number | null;
  group_id: number | null;
  amount: number | null;
  frequency: number | null;
  is_shared: boolean;
  shared_percent: number | null;
}

export const putClientCashflows = async (
  id: ClientId,
  payload: Payload,
): Promise<unknown> =>
  await apiClient.put(`/api/v1/clients/${id}/cashflows`, {
    body: payload,
  });
