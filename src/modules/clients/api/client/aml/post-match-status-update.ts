import { apiClient } from '@/services/api';
import { ClientId } from '@modules/clients';

export const postMatchStatusUpdate = async (
  clientId: ClientId,
  sessionId: number,
  matchId: number,
  matchStatus: string,
): Promise<boolean> => {
  return await apiClient.post(`/api/v1/clients/${clientId}/aml/monitoring`, {
    session_id: sessionId,
    match_id: matchId,
    match_status: matchStatus,
  });
};
