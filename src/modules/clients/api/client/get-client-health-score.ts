import { apiClient } from '@/services/api';

export interface GoalScoreDTO {
  goalId: number;
  score: number;
  auditResults: Record<string, any>;
}

export interface HealthScoreDTO {
  overallScore: number;
  goalScores: Record<number, GoalScoreDTO>;
  errors: Record<number, string>;
  warnings: Record<number, Record<number, string>>;
}

export interface HealthScoreApiResponse {
  overall_score: number;
  goal_scores: {
    [key: string]: {
      goal_id: number;
      score: number;
      audit_results: Record<string, any>;
    };
  };
  errors: Record<string, string>;
  warnings: Record<string, Record<string, string>>;
}

export function mapToHealthScoreDTO(
  response: HealthScoreApiResponse,
): HealthScoreDTO {
  return {
    overallScore: response.overall_score,
    goalScores: Object.entries(response.goal_scores).reduce(
      (acc, [key, value]) => ({
        ...acc,
        [Number(key)]: {
          goalId: value.goal_id,
          score: value.score,
          auditResults: value.audit_results,
        },
      }),
      {},
    ),
    errors: Object.entries(response.errors).reduce(
      (acc, [key, value]) => ({
        ...acc,
        [Number(key)]: value,
      }),
      {},
    ),
    warnings: Object.entries(response.warnings).reduce(
      (acc, [key, value]) => ({
        ...acc,
        [Number(key)]: Object.entries(value).reduce(
          (innerAcc, [innerKey, innerValue]) => ({
            ...innerAcc,
            [Number(innerKey)]: innerValue,
          }),
          {},
        ),
      }),
      {},
    ),
  };
}

export const getClientHealthScore = async (
  clientId: number,
): Promise<HealthScoreDTO> => {
  const response = await apiClient.get<Promise<HealthScoreApiResponse>>(
    `/api/v2/clients/${clientId}/health-score`,
  );
  try {
    return mapToHealthScoreDTO(response);
  } catch (error) {
    throw new Error('Failed to map health score response');
  }
};
