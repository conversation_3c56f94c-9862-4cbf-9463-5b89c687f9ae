import { apiClient } from '@/services/api';

import { Client } from '../../models';
import {
  AddressDTO,
  ClientAdvisorDTO,
  ClientDTO,
  LinkDTO,
  ReviewFrequency,
  ReviewMonth,
  mapReviewFrequencyToDomain,
} from '../../dtos';

//

export interface GetClientDTO
  extends Pick<
    ClientDTO,
    | 'id'
    | 'email'
    | 'no_email_reason_id'
    | 'title_id'
    | 'first_name'
    | 'last_name'
    | 'date_of_birth'
    | 'phone_number'
    | 'mobile_number'
    | 'marital_status_id'
    | 'birth_country_id'
    | 'primary_country_id'
    | 'secondary_country_id'
    | 'gender_id'
    | 'nationality_id'
    | 'type'
    | 'status'
    | 'client_type_id'
    | 'client_status_id'
    | 'client_source_id'
    | 'marketing_id'
    | 'client_agreement_id'
    | 'privacy_notice_id'
  > {
  links: LinkDTO[];
  addresses: AddressDTO[];
  advisor: ClientAdvisorDTO;
  review_frequency: ReviewFrequency;
  review_month: ReviewMonth;
  next_review_month: ReviewMonth;
  access_enabled: boolean;
}

export const getClient = async (id: Client['id']): Promise<Client> => {
  const clientDTO = await apiClient.get<GetClientDTO>(`/api/v1/clients/${id}`);

  return {
    addresses: clientDTO.addresses
      ? clientDTO.addresses.map((addressDTO) => ({
          addressLineOne: addressDTO.address_line_one,
          addressLineTwo: addressDTO.address_line_two,
          addressLineThree: addressDTO.address_line_three,
          addressLineFour: addressDTO.address_line_four,
          city: addressDTO.city,
          countryId: addressDTO.country_id,
          isPrimary: addressDTO.is_primary,
          postCode: addressDTO.post_code,
        }))
      : [],
    dateOfBirth: clientDTO.date_of_birth
      ? new Date(clientDTO.date_of_birth)
      : null,
    email: clientDTO.email,
    noEmailReasonId: clientDTO.no_email_reason_id,
    genderId: clientDTO.gender_id,
    id: clientDTO.id,
    lastName: clientDTO.last_name,
    maritalStatusId: clientDTO.marital_status_id,
    nationalityId: clientDTO.nationality_id,
    birthCountryId: clientDTO.birth_country_id,
    primaryCountryId: clientDTO.primary_country_id,
    secondaryCountryId: clientDTO.secondary_country_id,
    phoneNumber: clientDTO.phone_number,
    mobileNumber: clientDTO.mobile_number,
    linkedClients: clientDTO.links
      ? clientDTO.links.map((linkDTO) => ({
          firstName: linkDTO.first_name,
          lastName: linkDTO.last_name,
          linkedClientId: linkDTO.client_id,
          linkTypeId: linkDTO.link_relationship_id,
        }))
      : [],
    title: clientDTO.title_id,
    type: clientDTO.type,
    firstName: clientDTO.first_name,
    advisor: {
      id: clientDTO.advisor?.id,
      firstName: clientDTO.advisor?.first_name,
      lastName: clientDTO.advisor?.last_name,
    },
    clientSource: clientDTO.client_source_id,
    clientStatus: clientDTO.client_status_id,
    clientType: clientDTO.client_type_id,
    reviewFrequency: mapReviewFrequencyToDomain(clientDTO.review_frequency),
    reviewMonth: clientDTO.review_month,
    nextReviewMonth: clientDTO.next_review_month,
    marketingId: clientDTO.marketing_id,
    accessEnabled: clientDTO.access_enabled,
    clientAgreementId: clientDTO.client_agreement_id,
    privacyNoticeId: clientDTO.privacy_notice_id,
  };
};
