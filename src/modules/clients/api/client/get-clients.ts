import { ValidationError, array, number, object, string } from 'yup';
import { apiClient } from '@/services/api';
import { QueryParams } from '@/types/api';
import { ClientId } from '@modules/clients';
import { FetchClientsDataNotValidatedCorrectly } from '../../api/client/errors';
import {
  ReviewFrequency,
  mapToDomain as mapReviewFrequencyToDomain,
} from '../../dtos/review-frequency-dto';
import { type ClientList } from '../../models';
import { ReviewMonth } from '../../models/review-frequency';

const getClientsSchema = object({
  clients: array()
    .of(
      object({
        id: number().required(),
        email: string().nullable(),
        first_name: string().required(),
        last_name: string().nullable().defined(),
        client_type_id: number().required(),
        client_status_id: number().required(),
        owner_id: number().nullable().defined(),
        owner_first_name: string().nullable().defined(),
        owner_last_name: string().nullable().defined(),
        accounts: number().required(),
        links: number().required(),
        review_frequency: string().nullable(),
        review_month: number().nullable(),
      }),
    )
    .required(),
  total_client_count: number().required(),
});

const fetchClients = async (queryParams?: QueryParams): Promise<ClientList> => {
  try {
    const response = await apiClient.get<unknown>(
      '/api/v1/clients',
      queryParams,
    );

    const clientsDto = await getClientsSchema.validate(response);

    const clients: ClientList['clients'] = clientsDto.clients.map(
      (clientDTO) => ({
        id: clientDTO.id as ClientId,
        email: clientDTO.email ?? null,
        firstName: clientDTO.first_name,
        lastName: clientDTO.last_name ?? null,
        clientTypeId: clientDTO.client_type_id,
        clientStatusId: clientDTO.client_status_id,
        advisor: clientDTO.owner_id
          ? {
              id: clientDTO.owner_id,
              firstName: clientDTO.owner_first_name ?? '',
              lastName: clientDTO.owner_last_name ?? '',
            }
          : null,
        accountsCount: clientDTO.accounts,
        linksCount: clientDTO.links,
        reviewFrequency: clientDTO.review_frequency
          ? mapReviewFrequencyToDomain(
              clientDTO.review_frequency as ReviewFrequency,
            )
          : null,
        reviewMonth: clientDTO.review_month as ReviewMonth | null,
      }),
    );
    return {
      statuses: [],
      clients,
      totalClientCount: clientsDto.total_client_count,
    };
  } catch (e: unknown) {
    if (e instanceof ValidationError) {
      throw new FetchClientsDataNotValidatedCorrectly(e.message, e.cause);
    }

    return {
      statuses: [],
      clients: [],
      totalClientCount: 0,
    };
  }
};

export default fetchClients;
