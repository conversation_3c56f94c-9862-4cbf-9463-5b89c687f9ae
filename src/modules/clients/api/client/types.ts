// TODO: Check if used and deprecate if not
export interface FactfindDTO {
  is_experienced_financial_advice_before: boolean | null;
  investment_experience_id: number | null;
  ethical_investment: boolean | null;
  regious_restrictions: boolean | null;
  ni_number: string | null;
  is_will_in_place: number | null;
  is_lasting_power_of_attorney_in_place: number | null;
  credit_history_id: number | null;
  employment_status_id: number | null;
  retirement_age: number | null;
  monthly_retirement_income_required: number | null;
  state_pension: boolean | null;
  already_retired: boolean | null;
}
