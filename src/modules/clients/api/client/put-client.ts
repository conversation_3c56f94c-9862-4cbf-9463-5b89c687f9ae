import { apiClient } from '@/services/api';
import { DateTime } from '@/utils/dateTime';
import { Client } from '../../models';
import { ClientDTO, LinkDTO } from '../../dtos';

interface PutClientPayload
  extends Pick<
    Client,
    | 'clientType'
    | 'title'
    | 'firstName'
    | 'lastName'
    | 'dateOfBirth'
    | 'email'
    | 'noEmailReasonId'
    | 'phoneNumber'
    | 'mobileNumber'
    | 'clientSource'
    | 'clientStatus'
    | 'linkedClients'
    | 'clientAgreementId'
    | 'privacyNoticeId'
  > {
  clientOwner: Client['advisor']['id'];
}

interface PutClientDTO
  extends Pick<
    ClientDTO,
    | 'client_type_id'
    | 'title_id'
    | 'first_name'
    | 'last_name'
    | 'email'
    | 'no_email_reason_id'
    | 'client_status'
    | 'client_source'
    | 'owner_id'
    | 'date_of_birth'
    | 'phone_number'
    | 'mobile_number'
    | 'client_agreement_id'
    | 'privacy_notice_id'
  > {
  links: Pick<LinkDTO, 'link_relationship_id' | 'client_id'>[];
}

export const putClient = (
  id: Client['id'],
  payload: PutClientPayload,
): Promise<void> => {
  const putClientDTO: PutClientDTO = {
    date_of_birth: payload.dateOfBirth
      ? new DateTime(payload.dateOfBirth).formatForForm()
      : null,
    email: payload.email,
    no_email_reason_id: payload.noEmailReasonId,
    first_name: payload.firstName,
    last_name: payload.lastName,
    phone_number: payload.phoneNumber,
    mobile_number: payload.mobileNumber,
    title_id: payload.title,
    client_source: payload.clientSource,
    client_status: payload.clientStatus,
    client_type_id: payload.clientType,
    owner_id: payload.clientOwner,
    links: payload.linkedClients.map(($r) => ({
      link_relationship_id: $r.linkTypeId,
      client_id: $r.linkedClientId,
    })),
    client_agreement_id: payload.clientAgreementId,
    privacy_notice_id: payload.privacyNoticeId,
  };

  return apiClient.put<PutClientDTO, void>(
    `/api/v1/clients/${id}`,
    putClientDTO,
  );
};
