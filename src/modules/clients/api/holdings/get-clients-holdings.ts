import { map } from 'lodash';
import { apiClient } from '@/services/api';
import { Holding } from '../../models';
import { ClientHoldingDTO } from '../../dtos';

const prepareQuery = (clientIds: number[]) => {
  return clientIds.map((id) => `client_ids=${id}`).join('&');
};

export const getClientsActiveHoldings = async (
  clientIds: number[],
): Promise<Holding[]> => {
  const dtos = await apiClient.get<Promise<ClientHoldingDTO[]>>(
    `/api/v1/holdings/active-holdings?${prepareQuery(clientIds)}`,
  );

  return map(dtos, (dto) => ({
    id: dto.id,
    accountNumber: dto.account_number,
    subAccountNumber: dto.sub_account_number ?? '',
    clients: dto.clients,
    product: dto.product,
    provider: { id: dto.provider.id, name: dto.provider.name },
    feeSplitTemplate: dto.fee_split_template,
  }));
};
