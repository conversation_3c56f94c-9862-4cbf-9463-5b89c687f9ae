import { apiClient } from '@/services/api';
import { Holding } from '../../models';
import { ClientHoldingDTO } from '../../dtos';

export const getClientHoldings = async (id: number): Promise<Holding[]> => {
  const dtos = await apiClient.get<ClientHoldingDTO[]>(
    `/api/v1/clients/${id}/holdings-active`,
  );

  return dtos.map((dto) => ({
    id: dto.id,
    product: dto.product,
    clients: dto.clients,
    accountNumber: dto.account_number,
    provider: { id: dto.provider.id, name: dto.provider.name },
    subAccountNumber: dto.sub_account_number ?? '',
    feeSplitTemplate: dto.fee_split_template,
  }));
};
