import { defineStore } from 'pinia';

import { useRefData } from '@/stores';
import { AboutYou } from '@modules/factfind/types/AboutYou';
import { getClientGoals, putClientGoals } from '@modules/factfind/api';

import {
  ClientMarketingData,
  HealthScoreDTO,
  getClient,
  getClientHealthScore,
  getClientValuations,
  getIdentityValidationResult,
  getMarketingContact,
} from '../api';
import {
  Cashflow,
  Client,
  ClientGoal,
  ClientGoalTypeEnum,
  ClientTypeEnum,
  GoalWithHealthScore,
  Holding,
} from '../models';
import { ClientId } from '../types';
import { IdentityValidationResultDTO } from '../dtos';
//

export type ClientState = {
  client_id: ClientId;
  profile: Client;
  factfind: AboutYou;
  cashflows: Cashflow[]; // TODO: Deprecate
  goals: ClientGoal[];
  holdings: Holding[];
  valuations: unknown[];
  loaded: boolean;
  saving: boolean;
  amlIdentityValidationResult: IdentityValidationResultDTO | null;
  healthScore: HealthScoreDTO | null;
};

type Actions = {
  resetClient: () => void;
  loadClientData: (clientId: ClientId) => Promise<unknown>;
  loadClientMarketingData: (clientId: ClientId) => Promise<ClientMarketingData>;
  loadClientIdentityVerificationStatus: (
    clientId: ClientId,
  ) => Promise<unknown>;
  loadClientGoals: (clientId: ClientId) => Promise<unknown>;
  loadValuations: (clientId: ClientId) => Promise<unknown>;
  updateClientOverviewData: (data: {
    firstName: Client['firstName'];
    lastName: Client['lastName'];
    addresses: Client['addresses'];
    mobileNumber: Client['mobileNumber'];
    phoneNumber: Client['phoneNumber'];
    dateOfBirth: Client['dateOfBirth'] | null;
  }) => void;
  setGoals: (
    goal_id: number | null,
    custom_name: string | null,
  ) => Promise<void>;
  setClientIdentityVerificationStatus: (
    newStatus: string,
    newDate: string,
  ) => void;
  saveClientGoals: (goals: ClientGoal[]) => void;
  loadHealthScore: (clientId: ClientId) => Promise<unknown>;
};

type Getters = {
  getClientGoals: (state: ClientState) => ClientGoal[];
  getOnboardingGoal: (state: ClientState) => ClientGoal;
  getClientId: (state: ClientState) => Client['id'];
  getClientMarketingId: (state: ClientState) => Client['marketingId'];
  getProfile: (state: ClientState) => Client;
  getProfileCompleted: (state: ClientState) => boolean;
  getIdentityValidationStatus: (
    state: ClientState,
  ) => IdentityValidationResultDTO | null;
  getHealthScore: (state: ClientState) => HealthScoreDTO | null;
  getGoalsWithHealthScores: (
    state: ClientState,
  ) => Record<number, GoalWithHealthScore>; // goalId is the key
};

type StoreId = 'client' | 'linked-client';

export const useClientStore = (isLinkedClient?: boolean) =>
  defineStore<
    StoreId,
    ClientState,
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    Getters,
    Actions
  >(isLinkedClient ? 'linked-client' : 'client', {
    state: () => ({
      client_id: 0 as ClientId,
      profile: {
        id: 0 as ClientId,
        addresses: [],
        dateOfBirth: null,
        email: '',
        noEmailReasonId: null,
        firstName: '',
        genderId: null,
        lastName: '',
        maritalStatusId: null,
        nationalityId: null,
        birthCountryId: null,
        primaryCountryId: null,
        secondaryCountryId: null,
        phoneNumber: null,
        mobileNumber: null,
        linkedClients: [],
        title: 0,
        type: '',
        status: '',
        advisor: {
          id: 0,
          firstName: '',
          lastName: '',
        },
        clientSource: 0,
        clientStatus: 0,
        clientType: ClientTypeEnum.Individual,
        reviewMonth: null,
        nextReviewMonth: null,
        reviewFrequency: null,
        marketingId: null,
        accessEnabled: null,
        clientAgreementId: null,
        privacyNoticeId: null,
      },
      factfind: {} as AboutYou,
      cashflows: [], // TODO: Deprecate
      goals: [],
      holdings: [],
      valuations: [],
      loaded: false,
      saving: false,
      amlIdentityValidationResult: null,
      healthScore: null,
    }),
    getters: {
      getClientId: (state: ClientState) => state.client_id,
      getClientMarketingId: (state: ClientState) => state.profile.marketingId,
      getProfile: (state: ClientState) => state.profile,
      getIdentityValidationStatus: (state: ClientState) =>
        state.amlIdentityValidationResult,
      getClientGoals: (state: ClientState) =>
        state.goals.filter(
          (goal) => goal.goalTypeId !== ClientGoalTypeEnum.ClientSetup,
        ),
      getOnboardingGoal: (state: ClientState) =>
        state.goals.find(
          (goal) => goal.goalTypeId === ClientGoalTypeEnum.ClientSetup,
        ) as ClientGoal,
      getCashflows: (state: ClientState) => state.cashflows, // TODO: Deprecate
      getCashflowsForType() {
        return (type: number) => {
          const refDataStore = useRefData();

          const cashflowsForType: number[] =
            refDataStore.getCashflowsForType(type);
          return this.cashflows.filter((item: any) =>
            cashflowsForType.includes(item.income_expenditure_type_id),
          );
        };
      },
      getTotalHoldingsValueForType() {
        return (type: number) => {
          return this.getHoldingsForType(type).reduce(function (
            $a: number,
            holding: Holding,
          ) {
            const $b = holding.product
              ? holding.product.current_valuation.value
              : 0;
            return $a + $b;
          }, 0);
        };
      },
      getProfileCompleted: (state: ClientState) => {
        return !!state.profile.dateOfBirth && !!state.profile.addresses.length;
      },
      getHealthScore: (state: ClientState) => state.healthScore,
      getGoalsWithHealthScores: (state: ClientState) => {
        if (!state.healthScore || !state.goals) return {};

        // Map the goals with their health scores and create a record keyed by goalId
        return state.goals.reduce(
          (record, goal) => {
            record[goal.id] = {
              ...goal,
              healthScore: state.healthScore?.goalScores[goal.id] ?? {
                score: 0,
                auditResults: {},
              },
              warnings: state.healthScore?.warnings[goal.id] ?? {},
              errors: state.healthScore?.errors[goal.id] ?? '',
            };
            return record;
          },
          {} as Record<number, GoalWithHealthScore>,
        );
      },
    },
    actions: {
      resetClient() {
        this.$reset();
      },
      updateClientOverviewData(updatedData: {
        firstName: Client['firstName'];
        lastName: Client['lastName'];
        addresses: Client['addresses'];
        mobileNumber: Client['mobileNumber'];
        phoneNumber: Client['phoneNumber'];
        dateOfBirth: Client['dateOfBirth'];
      }) {
        this.profile.firstName = updatedData.firstName;
        this.profile.lastName = updatedData.lastName;
        this.profile.addresses = updatedData.addresses;
        this.profile.mobileNumber = updatedData.mobileNumber;
        this.profile.phoneNumber = updatedData.phoneNumber;
        this.profile.dateOfBirth = updatedData.dateOfBirth;
      },
      async loadClientData(clientId: ClientId) {
        this.client_id = clientId;
        try {
          this.profile = await getClient(clientId);
        } catch (error) {
          return error;
        } finally {
          this.loaded = true;
        }
      },
      async loadClientGoals(clientId: ClientId) {
        try {
          this.goals = await getClientGoals(clientId);
        } catch (error) {
          return error;
        }
      },
      async loadClientMarketingData(clientId: ClientId) {
        const marketing_details = await getMarketingContact(clientId);
        const { contact } = marketing_details;
        this.profile.marketingId = Number(contact?.id);
        return marketing_details;
      },
      async loadClientIdentityVerificationStatus(clientId: ClientId) {
        try {
          this.amlIdentityValidationResult =
            await getIdentityValidationResult(clientId);
        } catch (error) {
          // Should be a 404
          this.amlIdentityValidationResult = null;
        }
      },
      setClientIdentityVerificationStatus(newStatus: string, newDate: string) {
        this.amlIdentityValidationResult = {
          result: newStatus,
          date: newDate,
        };
      },
      saveClientGoals(goals: ClientGoal[]) {
        this.goals = goals;
      },
      async setGoals(
        goal_id: number | null,
        custom_name: string | null = null,
      ) {
        this.goals = await putClientGoals(this.client_id, {
          goal_id: goal_id,
          custom_name: custom_name,
        });
      },
      async loadValuations(clientId: ClientId) {
        this.client_id = clientId;
        this.valuations = await getClientValuations(clientId);
        this.loaded = true;
      },
      async loadHealthScore(clientId: number) {
        try {
          this.healthScore = await getClientHealthScore(clientId);
        } catch (error) {
          return error;
        }
      },
    },
  })();
