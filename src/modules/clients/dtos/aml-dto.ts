interface IdentityValidationSearchCriteriaDTO {
  first_name: string;
  last_name: string;
  address: string;
  city: string;
  post_code: string;
  country_code: string;
  date_of_birth: string;
}

interface IdentidyValidationResultDetailDTO {
  code: string;
  description: string;
}

export interface IdentityValidationResultDTO {
  id: number;
  result: string;
  date: string;
  detail: IdentidyValidationResultDetailDTO[];
  criteria: IdentityValidationSearchCriteriaDTO;
  verified: boolean;
  override_reason: string | null;
  override_user: string | null;
  override_date: string | null;
}

export interface Role {
  title: string;
}

export interface Match {
  id: string;
  first_name: string;
  last_name: string;
  image: string;
  categories: string[];
  roles: Role[];
  match_status: number;
}

export interface ClientMonitoringListDTO {
  id: string;
  matches: Match[];
  status: string;
}
