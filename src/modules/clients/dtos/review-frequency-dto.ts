import { ReviewFrequency as Domain } from '../models/review-frequency';

export type ReviewFrequency = 'ANNUAL' | 'SEMIANNUAL' | 'QUARTERLY';
export type ReviewMonth = number;

export function mapToBackend(domainValue: Domain): ReviewFrequency {
  const map = new Map<Domain, ReviewFrequency>([
    ['Annual', 'ANNUAL'],
    ['SemiAnnual', 'SEMIANNUAL'],
    ['Quarterly', 'QUARTERLY'],
  ]);

  return map.get(domainValue) as ReviewFrequency;
}

export function mapToDomain(backendValue: ReviewFrequency): Domain {
  const map = new Map<ReviewFrequency, Domain>([
    ['ANNUAL', 'Annual'],
    ['SEMIANNUAL', 'SemiAnnual'],
    ['QUARTERLY', 'Quarterly'],
  ]);

  return map.get(backendValue) as Domain;
}
