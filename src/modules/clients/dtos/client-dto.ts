import { ClientAdvisorDTO } from './client-advisor-dto';
import { ClientId } from '@modules/clients';
import { Nullable } from '@/types/Common';

export interface ClientDTO {
  id: ClientId;
  email: Nullable<string>;
  no_email_reason_id: Nullable<number>;
  title_id: number;
  first_name: string;
  last_name: Nullable<string>;
  date_of_birth: Nullable<string>;
  phone_number: Nullable<string>;
  mobile_number: Nullable<string>;
  marital_status_id: Nullable<number>;
  gender_id: Nullable<number>;
  nationality_id: Nullable<number>;
  birth_country_id: Nullable<number>;
  primary_country_id: Nullable<number>;
  secondary_country_id: Nullable<number>;
  type: string;
  status: string;
  client_status: number;
  client_type_id: number;
  client_status_id: number;
  client_source_id: number;
  client_source: number;
  owner_id: ClientAdvisorDTO['id'];
  marketing_id: Nullable<number>;
  client_agreement_id: Nullable<number>;
  privacy_notice_id: Nullable<number>;
}
