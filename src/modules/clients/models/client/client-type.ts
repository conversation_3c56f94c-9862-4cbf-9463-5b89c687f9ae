export enum ClientTypeEnum {
  Individual = 1,
  Trust = 2,
  Corporate = 3,
}

export type ClientType =
  | ClientTypeEnum.Individual
  | ClientTypeEnum.Trust
  | ClientTypeEnum.Corporate;

export const formatToView = (clientTypeId: ClientType) => {
  const map = new Map<ClientType, string>([
    [ClientTypeEnum.Individual, 'Individual'],
    [ClientTypeEnum.Trust, 'Trust'],
    [ClientTypeEnum.Corporate, 'Corporate'],
  ]);

  return map.get(clientTypeId);
};
