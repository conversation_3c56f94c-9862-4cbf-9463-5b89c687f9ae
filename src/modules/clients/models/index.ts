export {
  type C<PERSON>,
  type ClientList,
  type ClientL<PERSON>,
  type ClientType,
  type ClientStatus,
  type ClientStatuses,
  type ClientAdvisor,
  ClientTypeEnum,
  formatClientType,
  extractClientsFromClientProfile,
} from './client';
export type { Cashflow } from './cashflow';
export {
  type Goal,
  type ClientGoal,
  ClientGoalTypeEnum,
  type GoalWithHealthScore,
} from './goal';
export type { Relation } from './relation';
export type { Holding, GoalLinkedHolding } from './holding';
export type { Statuses } from './statuses';
export { ConnectionType } from './connection-type';
export type { ReviewFrequency, ReviewMonth } from './review-frequency';
export type { Note, ClientCaseGoalNote, FactfindNote } from './note';
export { default as useClientId } from './query-params/use-client-id';
