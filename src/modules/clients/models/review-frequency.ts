enum Months {
  January = 1,
  February = 2,
  March = 3,
  April = 4,
  May = 5,
  June = 6,
  July = 7,
  August = 8,
  September = 9,
  October = 10,
  November = 11,
  December = 12,
}

enum MonthNames {
  January = 'January',
  February = 'February',
  March = 'March',
  April = 'April',
  May = 'May',
  June = 'June',
  July = 'July',
  August = 'August',
  September = 'September',
  October = 'October',
  November = 'November',
  December = 'December',
}

export type ReviewFrequency = 'Annual' | 'SemiAnnual' | 'Quarterly';
export type ReviewMonth = Months;

export interface Review {
  getMonths(): [string[], ReviewMonth][];
}

export class AnnualReview implements Review {
  getMonths(): [string[], ReviewMonth][] {
    return [
      [[MonthNames.January], Months.January],
      [[MonthNames.February], Months.February],
      [[MonthNames.March], Months.March],
      [[MonthNames.April], Months.April],
      [[MonthNames.May], Months.May],
      [[MonthNames.June], Months.June],
      [[MonthNames.July], Months.July],
      [[MonthNames.August], Months.August],
      [[MonthNames.September], Months.September],
      [[MonthNames.October], Months.October],
      [[MonthNames.November], Months.November],
      [[MonthNames.December], Months.December],
    ];
  }
}

export class SemiAnnualReview implements Review {
  getMonths(): [string[], ReviewMonth][] {
    return [
      [[MonthNames.January, MonthNames.July], Months.January],
      [[MonthNames.February, MonthNames.August], Months.February],
      [[MonthNames.March, MonthNames.September], Months.March],
      [[MonthNames.April, MonthNames.October], Months.April],
      [[MonthNames.May, MonthNames.November], Months.May],
      [[MonthNames.June, MonthNames.December], Months.June],
    ];
  }
}

export class QuarterlyReview implements Review {
  getMonths(): [string[], ReviewMonth][] {
    return [
      [
        [
          MonthNames.January,
          MonthNames.April,
          MonthNames.July,
          MonthNames.October,
        ],
        Months.January,
      ],
      [
        [
          MonthNames.February,
          MonthNames.May,
          MonthNames.August,
          MonthNames.November,
        ],
        Months.February,
      ],
      [
        [
          MonthNames.March,
          MonthNames.June,
          MonthNames.September,
          MonthNames.December,
        ],
        Months.March,
      ],
    ];
  }
}

export class Frequency {
  constructor(private frequency: ReviewFrequency) {}

  toString(): string {
    const map: Map<ReviewFrequency, string> = new Map<ReviewFrequency, string>([
      ['Annual', 'Annual'],
      ['SemiAnnual', 'Semi-Annual'],
      ['Quarterly', 'Quarterly'],
    ]);

    return map.get(this.frequency) || '';
  }
}

export class Month {
  constructor(private monthNumber: number) {}

  toNumber(): number {
    return this.monthNumber;
  }

  toString(): string {
    const map: Map<number, string> = new Map<number, string>([
      [1, 'January'],
      [2, 'February'],
      [3, 'March'],
      [4, 'April'],
      [5, 'May'],
      [6, 'June'],
      [7, 'July'],
      [8, 'August'],
      [9, 'September'],
      [10, 'October'],
      [11, 'November'],
      [12, 'December'],
    ]);

    return (map.get(this.monthNumber) as string) || '';
  }
}

export function createFrequency(frequency: ReviewFrequency) {
  return new Frequency(frequency);
}

export function createMonth(monthNumber: number) {
  return new Month(monthNumber);
}
