import { Nullable } from '@/types/Common';
import { IDateTime } from '@/utils/dateTime';

interface NoteModelFactor {
  id: number;
  author: {
    id: number;
    firstName: Nullable<string>;
    lastName: Nullable<string>;
  };
  content: string;
  created: IDateTime;
}

export interface Note extends NoteModelFactor {}

export interface ClientCaseGoalNote
  extends Pick<NoteModelFactor, 'id' | 'author' | 'content' | 'created'> {}

export interface FactfindNote
  extends Pick<NoteModelFactor, 'id' | 'author' | 'content' | 'created'> {}
