import { Nullable } from '@/types/Common';
import { ClientGoalTypeEnum } from '@modules/clients/models';
import { ForecasterData, RiskProfileData } from '@modules/goals';

type LinkedHolding = {
  holding_id: number;
  account_number: string;
  provider_name: string;
  product_type: string;
};

export type ClientGoalDTO = Array<{
  goal_id: number;
  goal_name: string;
  linked_holdings: LinkedHolding[];
  risk_profile: RiskProfileData | null;
  cash_forecast: ForecasterData | null;
}>;

export type GoalAttrsDTO = {
  type_: ClientGoalTypeEnum;
  target_amount?: Nullable<number>;
  target_date?: Nullable<string>;
} & Record<string, any>;
