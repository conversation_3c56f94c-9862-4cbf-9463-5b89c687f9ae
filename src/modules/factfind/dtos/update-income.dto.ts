import { Income } from '../models';
import { FrequencyDto, FrequencyMapper } from './frequency.dto';

export type UpdateIncomeDto = Array<{
  id: number | null;
  frequency: FrequencyDto;
  amount: number;
  description: string;
  type_id: number;
}>;

export const mapper = (domains: Income[]): UpdateIncomeDto => {
  return domains.map((domain) => ({
    id: domain.id,
    type_id: domain.type,
    description: domain.description,
    frequency: FrequencyMapper.fromDomain(domain.frequency),
    amount: domain.amount.getValue(),
  }));
};
