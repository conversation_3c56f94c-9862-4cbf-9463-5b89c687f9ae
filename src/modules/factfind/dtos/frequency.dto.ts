import { Frequency, FrequencyEnum, IFrequency } from '../models';

export type FrequencyDto =
  | FrequencyDtoEnum.Daily
  | FrequencyDtoEnum.Weekly
  | FrequencyDtoEnum.Monthly
  | FrequencyDtoEnum.Fortnightly
  | FrequencyDtoEnum.Quarterly
  | FrequencyDtoEnum.Yearly;

export enum FrequencyDtoEnum {
  Daily = 'Daily',
  Weekly = 'Weekly',
  Monthly = 'Monthly',
  Fortnightly = 'Fortnightly',
  Quarterly = 'Quarterly',
  Yearly = 'Yearly',
}

export class FrequencyMapper {
  static toDomain(dto: FrequencyDto): Frequency {
    const frequencyMap = new Map<FrequencyDto, FrequencyEnum>([
      [FrequencyDtoEnum.Daily, FrequencyEnum.Daily],
      [FrequencyDtoEnum.Weekly, FrequencyEnum.Weekly],
      [FrequencyDtoEnum.Monthly, FrequencyEnum.Monthly],
      [FrequencyDtoEnum.Fortnightly, FrequencyEnum.Fortnightly],
      [FrequencyDtoEnum.Quarterly, FrequencyEnum.Quarterly],
      [FrequencyDtoEnum.Yearly, FrequencyEnum.Yearly],
    ]);

    return new Frequency(frequencyMap.get(dto) as FrequencyEnum);
  }
  static fromDomain(domain: IFrequency): FrequencyDto {
    const frequencyMap = new Map<FrequencyEnum, FrequencyDto>([
      [FrequencyEnum.Daily, FrequencyDtoEnum.Daily],
      [FrequencyEnum.Weekly, FrequencyDtoEnum.Weekly],
      [FrequencyEnum.Monthly, FrequencyDtoEnum.Monthly],
      [FrequencyEnum.Fortnightly, FrequencyDtoEnum.Fortnightly],
      [FrequencyEnum.Quarterly, FrequencyDtoEnum.Quarterly],
      [FrequencyEnum.Yearly, FrequencyDtoEnum.Yearly],
    ]);

    return frequencyMap.get(domain.toValue()) as FrequencyDto;
  }
}
