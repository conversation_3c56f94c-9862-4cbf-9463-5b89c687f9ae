import { Nullable } from '@/types/Common';

export interface FactfindDto {
  has_experienced_financial_advice_before: Nullable<boolean>;
  previous_investment_experience: Nullable<string>;
  wish_to_consider_ethical_investments: Nullable<boolean>;
  religious_restrictions: Nullable<boolean>;
  vulnerable_person: Nullable<boolean>;
  ni_number: Nullable<string>;
  has_will_in_place: Nullable<string>;
  has_power_of_attorney_in_place: Nullable<string>;
  credit_history: Nullable<string>;
  employment_status: Nullable<string>;
  retirement_age: Nullable<number>;
  monthly_retirement_income_required: Nullable<number>;
  state_pension: Nullable<boolean>;
  already_retired: Nullable<boolean>;
}
