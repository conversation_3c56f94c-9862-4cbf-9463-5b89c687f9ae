import { Expenditure } from '../models/expenditure';
import { FrequencyDto, FrequencyMapper } from './frequency.dto';

export type UpdateExpenditureDto = Array<{
  id: number | null;
  type_id: number;
  description: string;
  frequency: FrequencyDto;
  amount: number;
  is_essential: boolean;
}>;

export const mapper = (domains: Expenditure[]): UpdateExpenditureDto => {
  return domains.map((domain) => ({
    id: domain.id,
    type_id: domain.type,
    description: domain.description,
    frequency: FrequencyMapper.fromDomain(domain.frequency),
    amount: domain.amount.getValue(),
    is_essential: domain.isEssential,
  }));
};
