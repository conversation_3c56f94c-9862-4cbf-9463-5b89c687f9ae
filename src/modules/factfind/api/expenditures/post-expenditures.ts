import { apiClient } from '@/services/api';
import { ClientId } from '@modules/clients';
import { type Expenditure } from '../../models';
import {
  UpdateExpenditureDto,
  mapper,
} from '../../dtos/update-expenditure.dto';

export default async (
  clientId: ClientId,
  expenditures: Expenditure[],
): Promise<void> => {
  await apiClient.put<UpdateExpenditureDto, void>(
    `/api/v1/clients/${clientId}/factfind/expenditure`,
    mapper(expenditures),
  );
};
