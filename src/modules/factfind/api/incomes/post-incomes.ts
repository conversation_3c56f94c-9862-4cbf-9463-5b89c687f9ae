import { apiClient } from '@/services/api';
import { ClientId } from '@modules/clients';
import { type Income } from '../../models/income';
import { type UpdateIncomeDto, mapper } from '../../dtos/update-income.dto';

export default async (clientId: ClientId, incomes: Income[]): Promise<void> => {
  await apiClient.put<UpdateIncomeDto, void>(
    `/api/v1/clients/${clientId}/factfind/income`,
    mapper(incomes),
  );
};
