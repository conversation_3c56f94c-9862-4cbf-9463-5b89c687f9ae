// @deprecated
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { apiClient } from '@/services/api';
import { Goal } from '@modules/clients/models';
import { ClientId } from '@modules/clients';

interface Payload {
  goal_id: number | null;
  custom_name: string | null;
}

export const putClientGoals = async (
  id: ClientId,
  payload: Payload,
): Promise<Goal[]> =>
  await apiClient.put(`/api/v1/clients/${id}/goals`, {
    body: payload,
  });
