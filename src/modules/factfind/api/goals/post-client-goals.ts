import { apiClient } from '@/services/api';
import { ClientId } from '@modules/clients';

import { GoalAttrsDTO } from '../../dtos/goal.dto';
//

type Body = {
  goal_type_id: number;
  goal_name: string;
  client_ids: ClientId[];
  goal_attributes: Required<GoalAttrsDTO>;
};

export default async (clientId: ClientId, payload: Body): Promise<void> =>
  await apiClient.post<Body, Promise<void>>(
    `/api/v1/clients/${clientId}/factfind/goals`,
    payload,
  );
