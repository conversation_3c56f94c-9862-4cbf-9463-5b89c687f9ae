import { apiClient } from '@/services/api';
import { GoalId } from '@modules/goals';
import { ClientId } from '@modules/clients';

import { GoalAttrsDTO } from '../../dtos/goal.dto';
//

type Body = {
  goal_name: string;
  client_ids: ClientId[];
  goal_attributes: Required<GoalAttrsDTO>;
};

export default async (
  clientId: ClientId,
  goalId: GoalId,
  payload: Body,
): Promise<void> =>
  await apiClient.patch<Body, Promise<void>>(
    `/api/v1/clients/${clientId}/factfind/goals/${goalId}`,
    payload,
  );
