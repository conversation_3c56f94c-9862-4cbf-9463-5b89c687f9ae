import { apiClient } from '@/services/api';
import { ArrayElement } from '@/types/Common';
import { factory as dateTimeFactory } from '@/utils/dateTime';
import { ClientId } from '@modules/clients';
import { AboutYou, ClientDto } from '@modules/factfind';
//

interface AboutYouResponseData {
  client: ClientDto;
}

const mapAddress = (
  address: ArrayElement<ClientDto['addresses']>,
): ArrayElement<AboutYou['contactDetails']['addresses']> => ({
  id: address.id!,
  city: address.city,
  countryId: address.country_id!,
  addressLineOne: address.address_line_one!,
  addressLineTwo: address.address_line_two,
  addressLineThree: address.address_line_three,
  addressLineFour: address.address_line_four,
  moveInDate: address.moved_in_date
    ? dateTimeFactory(address.moved_in_date)
    : null,
  moveOutDate: address.moved_out_date
    ? dateTimeFactory(address.moved_out_date)
    : null,
  postCode: address.post_code!,
  isPrimary: address.is_primary,
});

const mapFamilyMember = (
  familyMember: ArrayElement<ClientDto['relations']>,
): ArrayElement<AboutYou['familyMembers']> => ({
  id: familyMember.id,
  firstName: familyMember.first_name,
  lastName: familyMember.last_name,
  dateOfBirth: familyMember.date_of_birth
    ? dateTimeFactory(familyMember.date_of_birth)
    : null,
  relationshipType: familyMember.relationship_type,
});

export default async (id: ClientId): Promise<AboutYou> => {
  const fetchedData = await apiClient.get<AboutYouResponseData>(
    `/api/v1/clients/${id}/factfind/primary-details`,
  );

  return {
    personalDetails: {
      firstName: fetchedData.client.first_name,
      lastName: fetchedData.client.last_name,
      dateOfBirth: fetchedData.client.date_of_birth
        ? dateTimeFactory(fetchedData.client.date_of_birth)
        : null,
      genderId: fetchedData.client.gender_id,
      maritalStatusId: fetchedData.client.marital_status_id,
      nationalityId: fetchedData.client.nationality_id,
      birthCountryId: fetchedData.client.birth_country_id,
      primaryCountryId: fetchedData.client.primary_country_id,
      secondaryCountryId: fetchedData.client.secondary_country_id,
      titleId: fetchedData.client.title_id,
    },
    contactDetails: {
      email: fetchedData.client.email_address,
      phoneNumber: fetchedData.client.phone_number,
      mobileNumber: fetchedData.client.mobile_number,
      addresses: fetchedData.client.addresses.map(mapAddress),
    },
    familyMembers: fetchedData.client.relations.map(mapFamilyMember),
    furtherInformations: {
      creditHistory: fetchedData.client.factfind.credit_history,
      employmentStatus: fetchedData.client.factfind.employment_status,
      ethicalInvestments:
        fetchedData.client.factfind.wish_to_consider_ethical_investments,
      insuranceNumber: fetchedData.client.factfind.ni_number,
      powerOfAttorney:
        fetchedData.client.factfind.has_power_of_attorney_in_place,
      previousFinancialAdvice:
        fetchedData.client.factfind.has_experienced_financial_advice_before,
      previousInvestmentExperience:
        fetchedData.client.factfind.previous_investment_experience,
      religiousRestrictions: fetchedData.client.factfind.religious_restrictions,
      isVulnerablePerson: fetchedData.client.factfind.vulnerable_person,
      will: fetchedData.client.factfind.has_will_in_place,
    },
    retirementDetails: {
      alreadyRetired: fetchedData.client.factfind.already_retired,
      monthlyRetirementIncomeRequired:
        fetchedData.client.factfind.monthly_retirement_income_required,
      retirementAge: fetchedData.client.factfind.retirement_age,
      statePension: fetchedData.client.factfind.state_pension,
    },
  };
};
