import { type ClientId } from '@modules/clients';
import { Asset } from '@modules/factfind/types/Asset';
import { Debt } from '@modules/factfind/types/Debt';
import { dtoToAssetMapper } from '@modules/factfind/utils/mappers/dtoToAssetMapper';
import { dtoToDebtMapper } from '@modules/factfind/utils/mappers/dtoToDebtMapper';
import { apiClient } from '@/services/api';
import { InferType, array, mixed, number, object, string } from 'yup';

const getAssetsAndDebtsSchema = object({
  assets: array()
    .of(
      object({
        id: number().required(),
        client_ids: array().of(mixed<ClientId>().required()).required(),
        group_id: number().required(),
        type_id: number().required(),
        status_id: number().required(),
        current_quantity: number().required(),
        attributes: object({}),
      }),
    )
    .required(),
  debts: array()
    .of(
      object({
        id: number().required(),
        client_ids: array().of(mixed<ClientId>().required()).required(),
        group_id: number().required(),
        type_id: number().required(),
        status_id: number().required(),
        current_quantity: number().required(),
        provider_id: number().required(),
        account_number: string().min(0).defined().nullable(),
        attributes: object({}),
      }),
    )
    .required(),
});

type AssetsAndDebtsDTO = InferType<typeof getAssetsAndDebtsSchema>;

export default async (
  id: ClientId,
): Promise<{ assets: Asset[]; debts: Debt[] }> => {
  const response = await apiClient.get<Promise<unknown>>(
    `/api/v1/clients/${id}/factfind/assets-and-debts`,
  );

  const assetsAndDebtsDTO: AssetsAndDebtsDTO =
    await getAssetsAndDebtsSchema.validate(response);

  const [assets, debts] = await Promise.all([
    Promise.all(
      assetsAndDebtsDTO.assets.map((asset) => dtoToAssetMapper(asset)),
    ),
    Promise.all(assetsAndDebtsDTO.debts.map((debt) => dtoToDebtMapper(debt))),
  ]);

  return {
    assets,
    debts,
  };
};
