import { apiClient } from '@/services/api';
import { ClientId } from '@modules/clients';
import { Asset, Debt } from '@modules/factfind/types';
import {
  PostAssetDTO,
  mapper as assetToDto,
} from '../../utils/mappers/asset-to-dto';
import {
  PostDebtDTO,
  mapper as debtToDto,
} from '../../utils/mappers/debt-to-dto';

type Body = {
  assets: PostAssetDTO[];
  debts: PostDebtDTO[];
};

type Response = {
  assets: PostAssetDTO[];
  debts: PostDebtDTO[];
};

export const assetsAndDebtsToDto = (assets: Asset[], debts: Debt[]): Body => ({
  assets: assets.map(assetToDto),
  debts: debts.map(debtToDto),
});

export default async (clientId: ClientId, assets: Asset[], debts: Debt[]) =>
  await apiClient.post<Body, Response>(
    `/api/v1/clients/${clientId}/factfind/assets-and-debts`,
    assetsAndDebtsToDto(assets, debts),
  );
