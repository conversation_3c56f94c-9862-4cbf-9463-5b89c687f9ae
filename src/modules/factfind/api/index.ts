export { default as getC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './get-client-about-you';
export { default as postClient<PERSON><PERSON>ut<PERSON><PERSON> } from './post-client-about-you';

export * from './incomes';
export * from './expenditures';
export * from './assets-debts';

// TODO: Review goals operations and remove unused/deprecated
export * from './goals/get-client-goals';
export * from './goals/put-client-goals';
export { default as postClientGoals } from './goals/post-client-goals';
export { default as deleteClientGoals } from './goals/delete-client-goals';
export { default as patchClientGoal } from './goals/patch-client-goal';
export { default as patchGoalHoldings } from './goals/patch-goal-holdings';
export { default as patchClientGoalObjectives } from './goals/patch-client-goal-objectives';

export { default as getClientSubscriptions } from './get-client-subscriptions';
export { default as getSubscriptionLists } from './get-subscription-lists';
export {
  subscribe as subscribeMarketingContact,
  unsubscribe as unsubscribeMarketingContact,
} from './update-client-subscription';
