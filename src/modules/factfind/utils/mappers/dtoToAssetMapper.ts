import { Money } from '@/utils/money';
import { ClientId } from '@modules/clients';
import { useProducts } from '@/composables/useProducts';
import {
  Account,
  CompanyShares,
  CryptoCurrency,
  DefinedBenefitPension,
  OtherAsset,
  Property,
} from '@modules/factfind/types/Asset';
import { Product } from '@modules/refdata/types/Product';
import { DateTime } from '@/utils/dateTime';
import {
  AccountDTO,
  CompanySharesDTO,
  CryptoCurrencyDTO,
  DefinedBenefitPensionDTO,
  OtherAssetDTO,
  PropertyDTO,
  accountSchema,
  companySharesSchema,
  cryptoCurrencySchema,
  definedBenefitPensionSchema,
  otherAssetSchema,
  propertySchema,
} from '@modules/factfind/services/validation/dtoToAssetValidator';
import {
  EstimatedRiskLevelEnum,
  PaymentDirectionEnum,
} from '@modules/accounts/models';

interface GetBaseAssetDTO {
  id: number;
  client_ids: ClientId[];
  group_id: number;
  type_id: number;
  status_id: number;
  attributes: Record<string, any>;
}

async function accountToDomain(dto: GetBaseAssetDTO) {
  const isValid = await accountSchema.isValid(dto);
  if (
    !isValid ||
    !(
      AssetDtoTypeGuard.isAccount(dto) ||
      AssetDtoTypeGuard.isTermPolicy(dto) ||
      AssetDtoTypeGuard.isIndemnityPolicy(dto) ||
      AssetDtoTypeGuard.isWholeOfLifePolicy(dto) ||
      AssetDtoTypeGuard.isIncomeProtectionPolicy(dto)
    )
  )
    return Promise.reject();

  return mapAccountToDomain(dto);
}

async function definedBenefitPensionToDomain(dto: GetBaseAssetDTO) {
  const isValid = await definedBenefitPensionSchema.isValid(dto);
  if (!isValid || !AssetDtoTypeGuard.isDefinedBenefitPension(dto))
    return Promise.reject();

  return mapDefinedBenefitPensionToDomain(dto);
}

async function propertyToDomain(dto: GetBaseAssetDTO) {
  const isValid = await propertySchema.isValid(dto);
  if (!isValid || !AssetDtoTypeGuard.isProperty(dto)) return Promise.reject();

  return mapPropertyToDomain(dto);
}

async function companySharesToDomain(dto: GetBaseAssetDTO) {
  const isValid = await companySharesSchema.isValid(dto);
  if (!isValid || !AssetDtoTypeGuard.isCompanyShares(dto))
    return Promise.reject();

  return mapCompanySharesToDomain(dto);
}

async function cryptoCurrencyToDomain(dto: GetBaseAssetDTO) {
  const isValid = await cryptoCurrencySchema.isValid(dto);
  if (!isValid || !AssetDtoTypeGuard.isCryptoCurrency(dto))
    return Promise.reject();

  return mapCryptoCurrencyToDomain(dto);
}

async function otherAssetToDomain(dto: GetBaseAssetDTO) {
  const isValid = await otherAssetSchema.isValid(dto);
  if (!isValid || !AssetDtoTypeGuard.isOtherAsset(dto)) return Promise.reject();

  return mapOtherAssetToDomain(dto);
}
class AssetDtoTypeGuard {
  static isAssetProduct = function (
    asset: GetBaseAssetDTO,
    products: Product[],
  ): boolean {
    return products.map((_) => _.id).includes(asset.type_id);
  };

  static isAccount = (asset: GetBaseAssetDTO): asset is AccountDTO => {
    const { getAccountProducts } = useProducts();
    return this.isAssetProduct(asset, getAccountProducts.value);
  };

  static isProperty = (asset: GetBaseAssetDTO): asset is PropertyDTO => {
    const { getPropertyProducts } = useProducts();
    return this.isAssetProduct(asset, getPropertyProducts.value);
  };

  static isCompanyShares = (
    asset: GetBaseAssetDTO,
  ): asset is CompanySharesDTO => {
    const { getCompanySharesProducts } = useProducts();
    return this.isAssetProduct(asset, getCompanySharesProducts.value);
  };

  static isCryptoCurrency = (
    asset: GetBaseAssetDTO,
  ): asset is CryptoCurrencyDTO => {
    const { getCryptoCurrencyProducts } = useProducts();
    return this.isAssetProduct(asset, getCryptoCurrencyProducts.value);
  };

  static isOtherAsset = (asset: GetBaseAssetDTO): asset is OtherAssetDTO => {
    const { getOtherAssetProducts } = useProducts();
    return this.isAssetProduct(asset, getOtherAssetProducts.value);
  };

  static isTermPolicy = (asset: GetBaseAssetDTO): asset is AccountDTO => {
    const { getTermPolicyProducts } = useProducts();
    return this.isAssetProduct(asset, getTermPolicyProducts.value);
  };

  static isIndemnityPolicy = (asset: GetBaseAssetDTO): asset is AccountDTO => {
    const { getIndemnityPolicyProducts } = useProducts();
    return this.isAssetProduct(asset, getIndemnityPolicyProducts.value);
  };

  static isWholeOfLifePolicy = (
    asset: GetBaseAssetDTO,
  ): asset is AccountDTO => {
    const { getWholeOfLifePolicyProducts } = useProducts();
    return this.isAssetProduct(asset, getWholeOfLifePolicyProducts.value);
  };

  static isIncomeProtectionPolicy = (
    asset: GetBaseAssetDTO,
  ): asset is AccountDTO => {
    const { getIncomeProtectionPolicyProducts } = useProducts();
    return this.isAssetProduct(asset, getIncomeProtectionPolicyProducts.value);
  };

  static isDefinedBenefitPension = (
    asset: GetBaseAssetDTO,
  ): asset is DefinedBenefitPensionDTO => {
    const { getDefinedBenefitPensionProducts } = useProducts();
    return this.isAssetProduct(asset, getDefinedBenefitPensionProducts.value);
  };
}

function mapValuationToDomain(
  dto: NonNullable<AccountDTO['valuation']>,
): Account['valuation'] {
  return {
    type: dto.is_actual ? 'actual' : 'estimate',
    date: new DateTime(dto.date),
    amount: new Money(dto.amount),
  };
}

const mapAccountToDomain = (dto: AccountDTO): Account => {
  try {
    const mapped = {
      id: dto.id,
      clientIds: dto.client_ids as ClientId[],
      groupId: dto.group_id,
      typeId: dto.type_id,
      statusId: dto.status_id,
      hasQuantity: Boolean(dto.current_quantity),
      accountNumber: dto.account_number ?? '',
      subAccountNumber: dto.sub_account_number ?? '',
      providerId: dto.provider_id,
      valuation: dto.valuation ? mapValuationToDomain(dto.valuation) : null,
      riskLevel: (dto.attributes.risk_level ??
        null) as EstimatedRiskLevelEnum | null,
      monthlyPaymentDirection: (dto.attributes.monthly_payment_direction ??
        null) as PaymentDirectionEnum | null,
      monthlyPaymentAmount: dto.attributes.monthly_payment_amount ?? null,
      coverAmount: dto.attributes.cover_amount ?? null,
      policyEndDate: dto.attributes.policy_end_date ?? null,
      monthlyBenefit: dto.attributes.monthly_benefit ?? null,
      deferredWeeks: dto.attributes.deferred_weeks ?? null,
    };
    return mapped;
  } catch (e) {
    console.error(e);
    throw e;
  }
};

const mapDefinedBenefitPensionToDomain = (
  dto: DefinedBenefitPensionDTO,
): DefinedBenefitPension => ({
  id: dto.id,
  clientIds: dto.client_ids as ClientId[],
  groupId: dto.group_id,
  typeId: dto.type_id,
  statusId: dto.status_id,
  hasQuantity: Boolean(dto.current_quantity),
  accountNumber: dto.account_number ?? '',
  subAccountNumber: dto.sub_account_number ?? '',
  providerId: dto.provider_id,
  valuation: dto.valuation ? mapValuationToDomain(dto.valuation) : null,
  isCurrentJob: dto.attributes.is_current_job ?? null,
  indexLinked: dto.attributes.index_linked ?? null,
  survivorBenefits: dto.attributes.survivor_benefits ?? null,
  estimatedAnnualIncomeAtRetirement:
    dto.attributes.estimated_annual_income_at_retirement ?? null,
  schemeNormalRetirementAge:
    dto.attributes.scheme_normal_retirement_age ?? null,
  accrualRate: dto.attributes.accrual_rate ?? null,
  predictedFinalSalary: dto.attributes.predicted_final_salary ?? null,
  predictedYearsOfServiceAtRetirement:
    dto.attributes.predicted_years_of_service_at_retirement ?? null,
});

const mapPropertyToDomain = (dto: PropertyDTO): Property => ({
  id: dto.id,
  clientIds: dto.client_ids as ClientId[],
  groupId: dto.group_id,
  typeId: dto.type_id,
  statusId: dto.status_id,
  hasQuantity: Boolean(dto.current_quantity),
  addressLineOne: dto.attributes.address_line_one,
  addressLineTwo: dto.attributes.address_line_two,
  city: dto.attributes.city,
  countryId: dto.attributes.country_id,
  owner: dto.attributes.ownership,
  postCode: dto.attributes.post_code,
  valuation: dto.valuation ? mapValuationToDomain(dto.valuation) : null,
});

const mapCompanySharesToDomain = (dto: CompanySharesDTO): CompanyShares => ({
  id: dto.id,
  clientIds: dto.client_ids as ClientId[],
  groupId: dto.group_id,
  typeId: dto.type_id,
  statusId: dto.status_id,
  hasQuantity: Boolean(dto.current_quantity),
  nameOfCompany: dto.attributes.name_of_company,
  valuation: dto.valuation ? mapValuationToDomain(dto.valuation) : null,
});

const mapCryptoCurrencyToDomain = (dto: CryptoCurrencyDTO): CryptoCurrency => ({
  id: dto.id,
  clientIds: dto.client_ids as ClientId[],
  groupId: dto.group_id,
  typeId: dto.type_id,
  statusId: dto.status_id,
  hasQuantity: Boolean(dto.current_quantity),
  nameOfCurrency: dto.attributes.name_of_currency,
  numberOfCoins: dto.attributes.number_of_coins,
  valuation: dto.valuation ? mapValuationToDomain(dto.valuation) : null,
});

const mapOtherAssetToDomain = (dto: OtherAssetDTO): OtherAsset => ({
  id: dto.id,
  clientIds: dto.client_ids as ClientId[],
  groupId: dto.group_id,
  typeId: dto.type_id,
  statusId: dto.status_id,
  hasQuantity: Boolean(dto.current_quantity),
  nameOfAsset: dto.attributes.name_of_asset,
  valuation: dto.valuation ? mapValuationToDomain(dto.valuation) : null,
});

export const dtoToAssetMapper = async (dto: GetBaseAssetDTO) => {
  return Promise.any([
    accountToDomain(dto),
    propertyToDomain(dto),
    companySharesToDomain(dto),
    cryptoCurrencyToDomain(dto),
    otherAssetToDomain(dto),
    definedBenefitPensionToDomain(dto),
  ]).catch((e) => {
    throw new Error(`Couldn't match asset (ID: ${dto.id}) to any known type`);
  });
};
