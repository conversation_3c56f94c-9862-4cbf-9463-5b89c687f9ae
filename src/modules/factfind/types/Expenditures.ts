import { Expenditure } from '../models/expenditure';

export type ExpenditureFormItem = {
  id: null | number;
  frequency: null | string;
  amount: number;
  description: string;
  new: boolean;
  valid: boolean;
  key: null | string;
  type: number;
  typeGroup: number;
  label: string;
  isEssential: boolean;
};

export type ExpenditureFormAction = Expenditure & {
  action: 'create' | 'edit' | 'delete';
  key?: string;
};
