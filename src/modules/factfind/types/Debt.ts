import { IMoney } from '@/utils/money';
import { IDateTime } from '@/utils/dateTime';
import { Nullable } from '@/types/Common';
import { Advisor } from '@modules/advisors';
import { ClientId } from '@modules/clients';
import { ListElement } from '@/composables/useListCrud';
import { Valuation } from '@modules/factfind/models';

export interface BaseDebt {
  id: Nullable<number>;
  clientIds: ClientId[];
  groupId: number;
  typeId: number;
  statusId?: number;
  hasQuantity: boolean;
  providerId: number;
  accountNumber: string;
  valuation: Valuation | null;
  pendingValuations?: Valuation[];
}

export interface Mortgage extends BaseDebt {
  securedAgainstAddressId: Nullable<number>;
  mortgageEndDate: Nullable<IDateTime>;
  interestRate: Nullable<number>;
  mortgageProductEndDate: Nullable<IDateTime>;
  monthlyPayment: Nullable<IMoney>;
  advisorId: Nullable<Advisor['id']>;
}

export interface PersonalLoan extends BaseDebt {
  securedAgainstAddressId: Nullable<number>;
  loanEndDate: Nullable<IDateTime>;
  interestRate: Nullable<number>;
  loanProductEndDate: Nullable<IDateTime>;
  monthlyPayment: Nullable<IMoney>;
  advisorId: Nullable<Advisor['id']>;
}

export interface CreditCard extends BaseDebt {
  interestRate: Nullable<number>;
  interestRateEndDate: Nullable<IDateTime>;
  monthlyPayment: Nullable<IMoney>;
  advisorId: Nullable<Advisor['id']>;
}

export interface OtherDebt extends BaseDebt {
  interestRate: Nullable<number>;
  interestRateEndDate: Nullable<IDateTime>;
  monthlyPayment: Nullable<IMoney>;
  advisorId: Nullable<Advisor['id']>;
}

export type Debt = Mortgage | PersonalLoan | CreditCard | OtherDebt;

export type DebtListItem = ListElement<Debt>;
