import { ListElement } from '@/composables/useListCrud';
import { Nullable } from '@/types/Common';
import { Country } from '@/types/Country';
import { ClientId } from '@modules/clients';
import { Valuation } from '@modules/factfind/models';
import {
  EstimatedRiskLevelEnum,
  PaymentDirectionEnum,
} from '@modules/accounts/models/account';

interface BaseAsset {
  id: Nullable<number>;
  clientIds: ClientId[];
  groupId: number;
  typeId: number;
  statusId?: number;
  hasQuantity: boolean;
  valuation: Valuation | null;
  pendingValuations?: Valuation[];
}

export interface Account extends BaseAsset {
  providerId: number;
  accountNumber: string;
  subAccountNumber: string;
  riskLevel: Nullable<EstimatedRiskLevelEnum>;
  monthlyPaymentDirection: Nullable<PaymentDirectionEnum>;
  monthlyPaymentAmount: Nullable<number>;
  coverAmount: Nullable<number>;
  policyEndDate: Nullable<string>;
  monthlyBenefit: Nullable<number>;
  deferredWeeks: Nullable<number>;
}

export interface DefinedBenefitPension extends BaseAsset {
  providerId: number;
  accountNumber: string;
  subAccountNumber: string;
  isCurrentJob: Nullable<boolean>;
  indexLinked: Nullable<boolean>;
  survivorBenefits: Nullable<boolean>;
  estimatedAnnualIncomeAtRetirement: Nullable<number>;
  schemeNormalRetirementAge: Nullable<number>;
  accrualRate: Nullable<number>;
  predictedFinalSalary: Nullable<number>;
  predictedYearsOfServiceAtRetirement: Nullable<number>;
}

export interface Property extends BaseAsset {
  addressLineOne: Nullable<string>;
  addressLineTwo: Nullable<string>;
  city: Nullable<string>;
  postCode: Nullable<string>;
  countryId: Country['id'];
  owner: Nullable<string>;
}

export interface CompanyShares extends BaseAsset {
  nameOfCompany: Nullable<string>;
}

export interface CryptoCurrency extends BaseAsset {
  nameOfCurrency: Nullable<string>;
  numberOfCoins: Nullable<number>;
}

export interface OtherAsset extends BaseAsset {
  nameOfAsset: Nullable<string>;
}

export type Asset =
  | Account
  | Property
  | CompanyShares
  | CryptoCurrency
  | OtherAsset
  | DefinedBenefitPension;

export type AssetListItem = ListElement<Asset>;
