import { InferType, array, boolean, mixed, number, object, string } from 'yup';
import type { ClientId } from '@modules/clients';

export type AccountDTO = InferType<typeof accountSchema>;
export type DefinedBenefitPensionDTO = InferType<
  typeof definedBenefitPensionSchema
>;
export type PropertyDTO = InferType<typeof propertySchema>;
export type CompanySharesDTO = InferType<typeof companySharesSchema>;
export type CryptoCurrencyDTO = InferType<typeof cryptoCurrencySchema>;
export type OtherAssetDTO = InferType<typeof otherAssetSchema>;

export const valuationSchema = object({
  id: number().nullable().required(),
  is_actual: boolean().required(),
  date: string().required(),
  amount: number().required(),
}).nullable();

export const accountSchema = object({
  id: number().required(),
  client_ids: array().of(mixed<ClientId>().required()).required(),
  group_id: number().required(),
  type_id: number().required(),
  status_id: number().required(),
  current_quantity: number().required(),
  attributes: object({
    risk_level: number().nullable(),
    monthly_payment_amount: number().nullable(),
    monthly_payment_direction: string().nullable(),
    cover_amount: number().nullable(),
    policy_end_date: string().nullable(),
    monthly_benefit: number().nullable(),
    deferred_weeks: number().nullable(),
  }),
  provider_id: number().required(),
  account_number: string().nullable().defined(),
  sub_account_number: string().nullable().defined(),
  valuation: valuationSchema,
});

export const definedBenefitPensionSchema = object({
  id: number().required(),
  client_ids: array().of(mixed<ClientId>().required()).required(),
  group_id: number().required(),
  type_id: number().required(),
  status_id: number().required(),
  current_quantity: number().required(),
  attributes: object({
    index_linked: boolean().nullable(),
    survivor_benefits: boolean().nullable(),
    is_current_job: boolean().nullable(),
    estimated_annual_income_at_retirement: number().nullable(),
    scheme_normal_retirement_age: number().nullable(),
    accrual_rate: number().nullable(),
    predicted_final_salary: number().nullable(),
    predicted_years_of_service_at_retirement: number().nullable(),
  }),
  provider_id: number().required(),
  account_number: string().nullable().defined(),
  sub_account_number: string().nullable().defined(),
  valuation: valuationSchema,
});

export const propertySchema = object({
  id: number().required(),
  client_ids: array().of(mixed<ClientId>().required()).required(),
  group_id: number().required(),
  type_id: number().required(),
  status_id: number().required(),
  current_quantity: number().required(),
  attributes: object({
    address_line_one: string().nullable().defined(),
    address_line_two: string().nullable().defined(),
    city: string().nullable().defined(),
    post_code: string().nullable().defined(),
    country_id: number().defined(),
    ownership: string().nullable().defined(),
  }).required(),
  valuation: valuationSchema,
});

export const companySharesSchema = object({
  id: number().required(),
  client_ids: array().of(mixed<ClientId>().required()).required(),
  group_id: number().required(),
  type_id: number().required(),
  status_id: number().required(),
  current_quantity: number().required(),
  attributes: object({
    name_of_company: string().nullable().defined(),
  }).required(),
  valuation: valuationSchema,
});

export const cryptoCurrencySchema = object({
  id: number().required(),
  client_ids: array().of(mixed<ClientId>().required()).required(),
  group_id: number().required(),
  type_id: number().required(),
  status_id: number().required(),
  current_quantity: number().required(),
  attributes: object({
    name_of_currency: string().nullable().defined(),
    number_of_coins: number().nullable().defined(),
  }).required(),
  valuation: valuationSchema,
});

export const otherAssetSchema = object({
  id: number().required(),
  client_ids: array().of(mixed<ClientId>().required()).required(),
  group_id: number().required(),
  type_id: number().required(),
  status_id: number().required(),
  current_quantity: number().required(),
  attributes: object({
    name_of_asset: string().nullable().defined(),
  }).required(),
  valuation: valuationSchema,
});
