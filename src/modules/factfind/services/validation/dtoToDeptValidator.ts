import { InferType, array, boolean, number, object, string } from 'yup';

export type MortgageDTO = InferType<typeof mortgageSchema>;
export type PersonalLoanDTO = InferType<typeof personalLoanSchema>;
export type CreditCardDTO = InferType<typeof creditCardSchema>;
export type OtherDebtDTO = InferType<typeof otherDebtSchema>;

export const valuationSchema = object({
  id: number().nullable().required(),
  is_actual: boolean().required(),
  date: string().required(),
  amount: number().required(),
}).nullable();

export const baseDebtSchema = object({
  id: number().required(),
  client_ids: array().of(number().required()).required(),
  group_id: number().required(),
  type_id: number().required(),
  status_id: number().required(),
  current_quantity: number().required(),
  provider_id: number().required(),
  account_number: string().min(0).defined(),
  valuation: valuationSchema,
});

export const mortgageSchema = baseDebtSchema.concat(
  object({
    attributes: object({
      mortgage_adviser_id: number().nullable().defined(),
      secured_against_address: number().nullable().defined(),
      mortgage_end_date: string().nullable().defined(),
      interest_rate: number().nullable().defined(),
      mortgage_product_end_date: string().nullable().defined(),
      monthly_payment: number().nullable().defined(),
    }).required(),
  }),
);

export const personalLoanSchema = baseDebtSchema.concat(
  object({
    adviser_id: number().nullable().defined(),
    attributes: object({
      secured_against_address: number().nullable().defined(),
      loan_end_date: string().nullable().defined(),
      interest_rate: number().nullable().defined(),
      loan_product_end_date: string().nullable().defined(),
      monthly_payment: number().nullable().defined(),
    }).required(),
  }),
);
export const creditCardSchema = baseDebtSchema.concat(
  object({
    adviser_id: number().nullable().defined(),
    attributes: object({
      interest_rate: number().nullable().defined(),
      interest_rate_end_date: string().nullable().defined(),
      monthly_payment: number().nullable().defined(),
    }).required(),
  }),
);

export const otherDebtSchema = baseDebtSchema.concat(
  object({
    adviser_id: number().nullable().defined(),
    attributes: object({
      interest_rate: number().nullable().defined(),
      interest_rate_end_date: string().nullable().defined(),
      monthly_payment: number().nullable().defined(),
    }).required(),
  }),
);
