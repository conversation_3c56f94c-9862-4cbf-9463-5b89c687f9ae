import { ListElement } from '@/utils/list';
import { IMoney } from '@/utils/money';
import { Nullable } from '@/types/Common';
import { IFrequency } from './frequency';

export interface Expenditure {
  id: Nullable<number>;
  typeGroup: number;
  type: number;
  description: string;
  frequency: IFrequency;
  amount: IMoney;
  isEssential: boolean;
}

export type ExpenditureListItem = ListElement<Expenditure>;
