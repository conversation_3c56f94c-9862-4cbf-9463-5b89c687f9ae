import { SelectOption } from '@/components/form/fields/field-model';
import { IMoney, Money } from '@/utils/money';

export interface IFrequency {
  toLabel: () => string;
  toValue: () => FrequencyEnum;
  calculateAmount: (amount: IMoney) => IMoney;
}

export enum FrequencyEnum {
  Daily = 'Daily',
  Weekly = 'Weekly',
  Monthly = 'Monthly',
  Fortnightly = 'Fortnightly',
  Quarterly = 'Quarterly',
  Yearly = 'Yearly',
}

const toStringMap = new Map<FrequencyEnum, string>([
  [FrequencyEnum.Daily, 'Daily'],
  [FrequencyEnum.Weekly, 'Weekly'],
  [FrequencyEnum.Monthly, 'Monthly'],
  [FrequencyEnum.Fortnightly, 'Fortnightly'],
  [FrequencyEnum.Quarterly, 'Quarterly'],
  [FrequencyEnum.Yearly, 'Yearly'],
]);

const amountMap = new Map<FrequencyEnum, (amount: number) => number>([
  [FrequencyEnum.Daily, (amount: number) => amount * 365],
  [FrequencyEnum.Weekly, (amount: number) => amount * 52],
  [FrequencyEnum.Monthly, (amount: number) => amount * 12],
  [FrequencyEnum.Fortnightly, (amount: number) => amount * 26],
  [FrequencyEnum.Quarterly, (amount: number) => amount * 4],
  [FrequencyEnum.Yearly, (amount: number) => amount],
]);

export class Frequency implements IFrequency {
  constructor(private value: FrequencyEnum) {}

  toLabel() {
    return toStringMap.get(this.value) || 'N/A';
  }

  toValue() {
    return this.value;
  }

  calculateAmount(amount: IMoney): IMoney {
    const calculateAmount = amountMap.get(this.value);
    return calculateAmount
      ? new Money(calculateAmount(amount.getValue()))
      : new Money(0);
  }
}

export const frequencySelectOptions: SelectOption[] = [...toStringMap].map(
  ([value, label]) => ({
    value,
    label,
  }),
);

export interface IAdviceFrequency {
  toLabel: () => string;
  toValue: () => AdviceFrequencyEnum;
}

export enum AdviceFrequencyEnum {
  WEEKLY = 'Weekly',
  MONTHLY = 'Monthly',
  EVERY_TWO_MONTHS = 'Every two months',
  QUARTERLY = 'Quarterly',
  SEMI_ANNUALLY = 'Every six months',
  ANNUALY = 'Annually',
}

const adviceFrequencyStringMap = new Map<AdviceFrequencyEnum, string>([
  [AdviceFrequencyEnum.WEEKLY, 'Weekly'],
  [AdviceFrequencyEnum.MONTHLY, 'Monthly'],
  [AdviceFrequencyEnum.EVERY_TWO_MONTHS, 'Every two months'],
  [AdviceFrequencyEnum.QUARTERLY, 'Quarterly'],
  [AdviceFrequencyEnum.SEMI_ANNUALLY, 'Every six months'],
  [AdviceFrequencyEnum.ANNUALY, 'Annually'],
]);

export class AdviceFrequency implements IAdviceFrequency {
  constructor(private value: AdviceFrequencyEnum) {}

  toLabel() {
    return adviceFrequencyStringMap.get(this.value) || 'N/A';
  }

  toValue() {
    return this.value;
  }
}

export const adviceFrequencySelectOptions: SelectOption[] = [
  ...adviceFrequencyStringMap,
].map(([value, label]) => ({
  value,
  label,
}));
