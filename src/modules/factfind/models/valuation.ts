import { IMoney, formatWithCurrency } from '@/utils/money';
import { IDateTime } from '@/utils/dateTime';
import { ValuationType } from '@modules/factfind/types';

export interface Valuation {
  id?: number;
  date: IDateTime;
  amount: IMoney;
  type: ValuationType;
}

export const formatToString = (valuation: Valuation) => {
  return `${formatWithCurrency(
    valuation.amount,
  )}, ${valuation.date.formatToView()}`;
};

export const formatToMoney = (valuation: Valuation) => {
  return formatWithCurrency(valuation.amount, {
    maximumFractionDigits: 2,
  });
};
