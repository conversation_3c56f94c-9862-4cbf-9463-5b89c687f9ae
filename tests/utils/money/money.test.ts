import { describe, expect, it } from 'vitest';
import { Money } from '@/utils/money';

describe('money', () => {
  describe('getValue', () => {
    it('returns numeric value', () => {
      expect(typeof new Money(12).getValue()).toBe('number');
    });
  });

  describe('add', () => {
    it('adds two values', () => {
      const money = new Money(0);
      money.add(new Money(10));

      expect(money.getValue()).toBe(10);
    });

    it('adds negative numbers', () => {
      const money = new Money(10);
      money.add(new Money(-20));

      expect(money.getValue()).toBe(-10);
    });

    it('adds decimal numbers', () => {
      const money = new Money(10.03);
      money.add(new Money(-20.1));
      money.add(new Money(14.12));

      expect(money.getValue()).toBe(4.05);
    });
  });

  describe('sum', () => {
    it('sums batch of money objects', () => {
      const money = new Money(0);
      money.sum([
        new Money(10),
        new Money(20),
        new Money(-35),
        new Money(15),
        new Money(5),
      ]);

      expect(money.getValue()).toBe(15);
    });
  });

  describe('substract', () => {
    it('substracts batch of decimal numbers', () => {
      const money = new Money(0);
      money.sum([
        new Money(24.63),
        new Money(13.67),
        new Money(13.67),
        new Money(336.97),
      ]);

      const result = new Money(388.94);
      result.substract(money);

      expect(result.getValue()).toBe(0);
    });
  });
});
