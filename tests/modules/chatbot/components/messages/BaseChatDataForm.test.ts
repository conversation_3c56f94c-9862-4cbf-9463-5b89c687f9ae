import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import * as yup from 'yup';
import BaseChatDataForm from '@modules/chatbot/components/messages/BaseChatDataForm.vue';
import { HttpMethod } from '@modules/chatbot/types/form-config';

// Mock dependencies
vi.mock('@/composables/useAPIState', () => ({
  useAPIState: () => ({ isLoading: false }),
}));

vi.mock('@modules/chatbot/components/messages/DynamicField.vue', () => ({
  default: {
    template:
      '<input :name="fieldName || field.key" :id="fieldId || field.key" :type="field.type === \'email\' ? \'email\' : \'text\'" :value="modelValue" @input="$emit(\'change\', $event.target.value)" />',
    props: [
      'field',
      'modelValue',
      'disabled',
      'readonly',
      'fieldName',
      'fieldId',
    ],
    emits: ['change'],
  },
}));

vi.mock('@modules/chatbot/components/messages/BaseChatDataCard.vue', () => ({
  default: {
    template: '<div data-testid="base-chat-data-card"><slot /></div>',
  },
}));

vi.mock('@/components/BaseButton.vue', () => ({
  default: {
    template:
      '<button :data-testid="$attrs[\'data-testid\']" :type="type" :disabled="isBusy || disabled"><slot /></button>',
    props: ['theme', 'isBusy', 'type', 'disabled'],
  },
}));

vi.mock('@modules/chatbot/types/form-config', async () => {
  const actual = await vi.importActual('@modules/chatbot/types/form-config');
  return {
    ...actual,
    getFieldsFromSchema: vi.fn(() => [
      { key: 'name', type: 'text', label: 'Name', required: true },
      { key: 'email', type: 'email', label: 'Email', required: true },
      { key: 'age', type: 'text', label: 'Age', required: false },
    ]),
  };
});

describe('BaseChatDataForm', () => {
  const mockSchema = yup.object({
    name: yup.string().required(),
    email: yup.string().email().required(),
    age: yup.number(),
  });

  const mockMessage = {
    id: '1',
    timestamp: '2023-01-01T00:00:00Z',
    messageLayout: 'test',
    availableFields: ['name', 'email', 'age'],
    method: HttpMethod.POST,
    completed: false,
    changesMade: false,
    name: 'John Doe',
    email: '<EMAIL>',
    age: '30',
  };

  const mockConfig = {
    title: 'Test Form',
    validationSchema: 'test_schema',
  };

  const mockOnSubmit = vi.fn();

  // Helper function to mock getFieldsFromSchema
  const mockGetFieldsFromSchema = async (fields: any[]) => {
    const { getFieldsFromSchema } = await import(
      '@modules/chatbot/types/form-config'
    );
    vi.mocked(getFieldsFromSchema).mockReturnValueOnce(fields);
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('renders form with correct fields from schema', () => {
    const wrapper = mount(BaseChatDataForm, {
      props: {
        config: mockConfig,
        message: mockMessage,
        onSubmit: mockOnSubmit,
        validationSchema: mockSchema,
      },
    });

    // Check that form fields are rendered
    expect(wrapper.find('label[for="name"]').text()).toBe('Name:');
    expect(wrapper.find('label[for="email"]').text()).toBe('Email:');
    expect(wrapper.find('label[for="age"]').text()).toBe('Age:');
  });

  it('applies field overrides correctly', async () => {
    const fieldOverrides = {
      email: { type: 'text' as const, label: 'Email Address' },
      age: { type: 'date' as const },
    };

    await mockGetFieldsFromSchema([
      { key: 'name', type: 'text', label: 'Name', required: true },
      { key: 'email', type: 'text', label: 'Email Address', required: true },
      { key: 'age', type: 'date', label: 'Age', required: false },
    ]);

    const wrapper = mount(BaseChatDataForm, {
      props: {
        config: mockConfig,
        message: mockMessage,
        onSubmit: mockOnSubmit,
        validationSchema: mockSchema,
        fieldOverrides,
      },
    });

    // Check that label overrides are applied
    expect(wrapper.find('label[for="email"]').text()).toBe('Email Address:');
    expect(wrapper.find('label[for="age"]').text()).toBe('Age:');

    // Verify that getFieldsFromSchema was called (proving overrides were processed)
    const { getFieldsFromSchema } = await import(
      '@modules/chatbot/types/form-config'
    );
    expect(vi.mocked(getFieldsFromSchema)).toHaveBeenCalled();
  });

  it('shows edit mode for non-GET methods', () => {
    const wrapper = mount(BaseChatDataForm, {
      props: {
        config: mockConfig,
        message: mockMessage,
        onSubmit: mockOnSubmit,
        validationSchema: mockSchema,
      },
    });

    // Should show Cancel and Confirm buttons in edit mode
    expect(wrapper.find('[data-testid="cancel-button"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="confirm-button"]').exists()).toBe(true);
  });

  it('shows edit button for GET method', () => {
    const readOnlyMessage = { ...mockMessage, method: HttpMethod.GET };

    const wrapper = mount(BaseChatDataForm, {
      props: {
        config: mockConfig,
        message: readOnlyMessage,
        onSubmit: mockOnSubmit,
        validationSchema: mockSchema,
      },
    });

    // Should show Edit button in read-only mode
    expect(wrapper.find('[data-testid="edit-button"]').exists()).toBe(true);
  });

  it('validates required fields', async () => {
    const invalidMessage = { ...mockMessage, name: '', email: '', age: '30' };

    const wrapper = mount(BaseChatDataForm, {
      props: {
        config: mockConfig,
        message: invalidMessage,
        onSubmit: mockOnSubmit,
        validationSchema: mockSchema,
      },
    });

    // Try to submit form with invalid data
    await wrapper.find('[data-testid="chat-data-form"]').trigger('submit');
    await wrapper.vm.$nextTick();

    // Wait for validation
    vi.advanceTimersByTime(100);
    await wrapper.vm.$nextTick();

    // Form should not submit with validation errors
    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('handles array fields correctly', async () => {
    const arraySchema = yup.object({
      addresses: yup.array().of(
        yup.object({
          id: yup.number(),
          street: yup.string().required(),
          city: yup.string().required(),
        }),
      ),
    });

    const arrayMessage = {
      ...mockMessage,
      availableFields: ['addresses'],
      addresses: [
        { id: 1, street: '123 Main St', city: 'New York' },
        { id: 2, street: '456 Oak Ave', city: 'Boston' },
      ],
    };

    await mockGetFieldsFromSchema([
      {
        key: 'addresses',
        type: 'array',
        label: 'Addresses',
        required: false,
        arrayItemSchema: {
          fields: {
            id: { type: 'number' },
            street: { type: 'string', tests: [{ name: 'required' }] },
            city: { type: 'string', tests: [{ name: 'required' }] },
          },
        },
      },
    ]);

    const wrapper = mount(BaseChatDataForm, {
      props: {
        config: mockConfig,
        message: arrayMessage,
        onSubmit: mockOnSubmit,
        validationSchema: arraySchema,
      },
    });

    // Check that array items are rendered
    const streetLabels = wrapper
      .findAll('label')
      .filter((label) => label.text().includes('Street'));
    expect(streetLabels).toHaveLength(2);
  });

  it('enables edit mode when edit button is clicked', async () => {
    const readOnlyMessage = {
      ...mockMessage,
      method: HttpMethod.GET,
      completed: false, // Ensure not completed
    };

    const wrapper = mount(BaseChatDataForm, {
      props: {
        config: mockConfig,
        message: readOnlyMessage,
        onSubmit: mockOnSubmit,
        validationSchema: mockSchema,
      },
    });

    // Initially should show Edit button (for GET method in read-only mode)
    const editButton = wrapper.find('[data-testid="edit-button"]');
    expect(editButton.exists()).toBe(true);

    // Click edit button
    await editButton.trigger('click');
    await wrapper.vm.$nextTick();

    // Should now show Cancel and Confirm buttons
    expect(wrapper.find('[data-testid="cancel-button"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="confirm-button"]').exists()).toBe(true);
  });

  it('cancels edit mode and restores original values', async () => {
    const readOnlyMessage = {
      ...mockMessage,
      method: HttpMethod.GET,
      completed: false, // Ensure not completed
      name: 'Original Name',
    };

    const wrapper = mount(BaseChatDataForm, {
      props: {
        config: mockConfig,
        message: readOnlyMessage,
        onSubmit: mockOnSubmit,
        validationSchema: mockSchema,
        initialValues: {
          name: 'Original Name',
          email: '<EMAIL>',
          age: '30',
        },
      },
    });

    // Enable edit mode
    const editButton = wrapper.find('[data-testid="edit-button"]');
    await editButton.trigger('click');
    await wrapper.vm.$nextTick();

    // Change a field value using the name input
    const nameInput = wrapper.find('input[name="name"]');
    expect(nameInput.exists()).toBe(true);

    await nameInput.setValue('Changed Name');
    await wrapper.vm.$nextTick();

    // Verify the value was changed
    expect((nameInput.element as HTMLInputElement).value).toBe('Changed Name');

    // Click cancel button
    const cancelButton = wrapper.find('[data-testid="cancel-button"]');
    await cancelButton.trigger('click');
    await wrapper.vm.$nextTick();

    // Should return to read-only mode
    expect(wrapper.find('[data-testid="edit-button"]').exists()).toBe(true);

    // Re-enter edit mode to verify value was restored
    await wrapper.find('[data-testid="edit-button"]').trigger('click');
    await wrapper.vm.$nextTick();

    // Verify the original value was restored
    const restoredNameInput = wrapper.find('input[name="name"]');
    expect(restoredNameInput.exists()).toBe(true);
    expect((restoredNameInput.element as HTMLInputElement).value).toBe(
      'Original Name',
    );
  });

  it('handles completed message state correctly', () => {
    const completedMessage = { ...mockMessage, completed: true };

    const wrapper = mount(BaseChatDataForm, {
      props: {
        config: mockConfig,
        message: completedMessage,
        onSubmit: mockOnSubmit,
        validationSchema: mockSchema,
      },
    });

    // Should not show any buttons when completed
    expect(wrapper.findAll('button')).toHaveLength(0);

    // Fieldset should be disabled
    expect(wrapper.find('fieldset').attributes('disabled')).toBeDefined();
  });

  it('handles field handlers for array items', async () => {
    const mockFieldHandler = vi.fn();
    const fieldHandlers = {
      isPrimary: { onChange: mockFieldHandler },
    };

    const arraySchema = yup.object({
      addresses: yup.array().of(
        yup.object({
          id: yup.number(),
          street: yup.string(),
          isPrimary: yup.boolean(),
        }),
      ),
    });

    const arrayMessage = {
      ...mockMessage,
      availableFields: ['addresses'],
      addresses: [{ id: 1, street: '123 Main St', isPrimary: false }],
    };

    await mockGetFieldsFromSchema([
      {
        key: 'addresses',
        type: 'array',
        label: 'Addresses',
        required: false,
        arrayItemSchema: {
          fields: {
            id: { type: 'number' },
            street: { type: 'string' },
            isPrimary: { type: 'boolean' },
          },
        },
      },
    ]);

    const wrapper = mount(BaseChatDataForm, {
      props: {
        config: mockConfig,
        message: arrayMessage,
        onSubmit: mockOnSubmit,
        validationSchema: arraySchema,
        fieldHandlers,
      },
    });

    // Find the isPrimary input field for the first address
    const isPrimaryInput = wrapper.find('input[name="addresses[0].isPrimary"]');
    expect(isPrimaryInput.exists()).toBe(true);

    // Change the value of the isPrimary field
    await isPrimaryInput.setValue('true');
    await wrapper.vm.$nextTick();

    // Verify that the field handler was called with correct parameters
    expect(mockFieldHandler).toHaveBeenCalledWith('true', 0);
  });

  it('handles different field types correctly', async () => {
    const mixedSchema = yup.object({
      name: yup.string().required(),
      email: yup.string().email().required(),
      birthDate: yup.string().test('is-valid-date', 'Invalid date', () => true),
      isActive: yup.boolean(),
      country: yup.string(),
    });

    await mockGetFieldsFromSchema([
      { key: 'name', type: 'text', label: 'Name', required: true },
      { key: 'email', type: 'email', label: 'Email', required: true },
      { key: 'birthDate', type: 'date', label: 'Birth Date', required: false },
      {
        key: 'isActive',
        type: 'checkbox',
        label: 'Is Active',
        required: false,
      },
      {
        key: 'country',
        type: 'select',
        label: 'Country',
        required: false,
        options: [{ value: 'US', label: 'United States' }],
      },
    ]);

    const mixedMessage = {
      ...mockMessage,
      availableFields: ['name', 'email', 'birthDate', 'isActive', 'country'],
      birthDate: '2023-01-01',
      isActive: true,
      country: 'US',
    };

    const wrapper = mount(BaseChatDataForm, {
      props: {
        config: mockConfig,
        message: mixedMessage,
        onSubmit: mockOnSubmit,
        validationSchema: mixedSchema,
      },
    });

    // Check that different field types are rendered
    expect(wrapper.find('label[for="name"]').text()).toBe('Name:');
    expect(wrapper.find('label[for="email"]').text()).toBe('Email:');
    expect(wrapper.find('label[for="birthDate"]').text()).toBe('Birth Date:');
    expect(wrapper.find('label[for="isActive"]').text()).toBe('Is Active:');
    expect(wrapper.find('label[for="country"]').text()).toBe('Country:');
  });

  it('handles null and undefined field values', () => {
    const nullMessage = {
      ...mockMessage,
      name: null,
      email: undefined,
      age: '',
    };

    const wrapper = mount(BaseChatDataForm, {
      props: {
        config: mockConfig,
        message: nullMessage,
        onSubmit: mockOnSubmit,
        validationSchema: mockSchema,
      },
    });

    // Should render without errors
    expect(wrapper.find('[data-testid="chat-data-form"]').exists()).toBe(true);

    // Verify that null/undefined values are handled as empty strings in inputs
    const nameInput = wrapper.find('input[name="name"]');
    const emailInput = wrapper.find('input[name="email"]');
    const ageInput = wrapper.find('input[name="age"]');

    expect(nameInput.exists()).toBe(true);
    expect(emailInput.exists()).toBe(true);
    expect(ageInput.exists()).toBe(true);

    // Null and undefined should be displayed as empty values
    expect((nameInput.element as HTMLInputElement).value).toBe('');
    expect((emailInput.element as HTMLInputElement).value).toBe('');
    expect((ageInput.element as HTMLInputElement).value).toBe('');
  });

  it('applies field overrides for default values and labels', async () => {
    const fieldOverrides = {
      country: {
        type: 'text' as const,
        defaultValue: 'United States',
        label: 'Custom Country',
      },
      city: { type: 'text' as const, defaultValue: 'New York' },
    };

    const messageWithOverrides = {
      ...mockMessage,
      availableFields: ['name', 'country', 'city'],
      country: undefined, // Should use default
      city: null, // Should use default
    };

    await mockGetFieldsFromSchema([
      { key: 'name', type: 'text', label: 'Name', required: true },
      {
        key: 'country',
        type: 'text',
        label: 'Custom Country',
        required: false,
      },
      { key: 'city', type: 'text', label: 'City', required: false },
    ]);

    const wrapper = mount(BaseChatDataForm, {
      props: {
        config: mockConfig,
        message: messageWithOverrides,
        onSubmit: mockOnSubmit,
        validationSchema: mockSchema,
        fieldOverrides,
      },
    });

    // Verify field overrides are applied
    const countryLabel = wrapper.find('label[for="country"]');
    const countryInput = wrapper.find('input[name="country"]');
    const cityInput = wrapper.find('input[name="city"]');
    const nameInput = wrapper.find('input[name="name"]');

    expect(countryLabel.exists()).toBe(true);
    expect(countryInput.exists()).toBe(true);
    expect(cityInput.exists()).toBe(true);
    expect(nameInput.exists()).toBe(true);

    // Should show custom label from override
    expect(countryLabel.text()).toBe('Custom Country:');

    // Should show default values from overrides for null/undefined fields
    expect((countryInput.element as HTMLInputElement).value).toBe(
      'United States',
    );
    expect((cityInput.element as HTMLInputElement).value).toBe('New York');

    // Field without override should show actual message value
    expect((nameInput.element as HTMLInputElement).value).toBe('John Doe');
  });

  it('preserves existing values over default values', async () => {
    const fieldOverrides = {
      country: { type: 'text' as const, defaultValue: 'United States' },
    };

    const messageWithValues = {
      ...mockMessage,
      availableFields: ['country'],
      country: 'Canada', // Has actual value, should not use default
    };

    await mockGetFieldsFromSchema([
      { key: 'country', type: 'text', label: 'Country', required: false },
    ]);

    const wrapper = mount(BaseChatDataForm, {
      props: {
        config: mockConfig,
        message: messageWithValues,
        onSubmit: mockOnSubmit,
        validationSchema: mockSchema,
        fieldOverrides,
      },
    });

    const countryInput = wrapper.find('input[name="country"]');
    expect(countryInput.exists()).toBe(true);

    // Should show actual value, not default from override
    expect((countryInput.element as HTMLInputElement).value).toBe('Canada');
  });

  it('handles cancel for non-GET methods', async () => {
    const editableMessage = {
      ...mockMessage,
      method: HttpMethod.POST,
      completed: false, // Ensure not completed
    };

    const wrapper = mount(BaseChatDataForm, {
      props: {
        config: mockConfig,
        message: editableMessage,
        onSubmit: mockOnSubmit,
        validationSchema: mockSchema,
      },
    });

    // For non-GET methods, should be in edit mode by default
    expect(wrapper.find('[data-testid="cancel-button"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="confirm-button"]').exists()).toBe(true);

    const cancelButton = wrapper.find('[data-testid="cancel-button"]');
    await cancelButton.trigger('click');
    await wrapper.vm.$nextTick();

    // Wait for async operations
    vi.advanceTimersByTime(100);
    await wrapper.vm.$nextTick();

    // Should call onSubmit with completed=true and changesMade=false
    expect(mockOnSubmit).toHaveBeenCalledWith(
      expect.objectContaining({
        completed: true,
        changesMade: false,
      }),
    );
  });

  it('handles empty string values correctly with overrides', async () => {
    const fieldOverrides = {
      country: { type: 'text' as const, defaultValue: 'United States' },
    };

    const messageWithEmptyString = {
      ...mockMessage,
      availableFields: ['country'],
      country: '', // Empty string should not use default
    };

    await mockGetFieldsFromSchema([
      { key: 'country', type: 'text', label: 'Country', required: false },
    ]);

    const wrapper = mount(BaseChatDataForm, {
      props: {
        config: mockConfig,
        message: messageWithEmptyString,
        onSubmit: mockOnSubmit,
        validationSchema: mockSchema,
        fieldOverrides,
      },
    });

    const countryInput = wrapper.find('input[name="country"]');
    expect(countryInput.exists()).toBe(true);

    // Empty string is a valid value, should not use default
    expect((countryInput.element as HTMLInputElement).value).toBe('');
  });

  it('disables confirm button when cancel is loading', async () => {
    const editableMessage = {
      ...mockMessage,
      method: HttpMethod.POST,
      completed: false,
    };

    // Mock onSubmit to return a promise that we can control
    let resolveSubmit: () => void;
    const pendingSubmit = new Promise<void>((resolve) => {
      resolveSubmit = resolve;
    });
    const controlledMockOnSubmit = vi.fn().mockReturnValue(pendingSubmit);

    const wrapper = mount(BaseChatDataForm, {
      props: {
        config: mockConfig,
        message: editableMessage,
        onSubmit: controlledMockOnSubmit,
        validationSchema: mockSchema,
      },
    });

    const cancelButton = wrapper.find('[data-testid="cancel-button"]');
    const confirmButton = wrapper.find('[data-testid="confirm-button"]');

    // Initially neither button should be disabled
    expect(cancelButton.attributes('disabled')).toBeUndefined();
    expect(confirmButton.attributes('disabled')).toBeUndefined();

    // Click cancel to start loading
    await cancelButton.trigger('click');
    await wrapper.vm.$nextTick();

    // Now confirm button should be disabled while cancel is loading
    expect(confirmButton.attributes('disabled')).toBeDefined();

    // Resolve the promise to finish loading
    resolveSubmit!();
    await wrapper.vm.$nextTick();

    expect(controlledMockOnSubmit).toHaveBeenCalled();
  });
});
