import { beforeEach, describe, expect, it, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import UserHealthScore from '@modules/chatbot/components/messages/data/UserHealthScore.vue';
import { User_HealthScore_FormValues } from '@modules/chatbot/types/form-model';

vi.mock('chart.js', () => ({
  Chart: Object.assign(
    vi.fn().mockImplementation(() => ({
      destroy: vi.fn(),
    })),
    {
      register: vi.fn(),
    },
  ),
  CategoryScale: {},
  Filler: {},
  LineController: {},
  LineElement: {},
  LinearScale: {},
  PointElement: {},
  TimeScale: {},
  Title: {},
  Tooltip: {},
}));

// Mock chartjs-adapter-date-fns
vi.mock('chartjs-adapter-date-fns');

// Mock HTMLCanvasElement.getContext
Object.defineProperty(HTMLCanvasElement.prototype, 'getContext', {
  value: vi.fn(() => ({
    createLinearGradient: vi.fn(() => ({
      addColorStop: vi.fn(),
    })),
  })),
  writable: true,
});

// Stub the chart components
const ScoreGaugeStub = {
  name: 'ScoreGauge',
  template:
    '<div data-testid="score-gauge-stub">Score: {{ score }}/{{ maxScore }}</div>',
  props: ['score', 'maxScore'],
};

const ScoreDonutStub = {
  name: 'ScoreDonut',
  template: '<div data-testid="score-donut-stub">Score: {{ score }}</div>',
  props: ['score'],
};

describe('UserHealthScore.vue', () => {
  let wrapper: ReturnType<typeof mount>;
  let mockMessage: User_HealthScore_FormValues;

  beforeEach(() => {
    vi.clearAllMocks();

    mockMessage = {
      id: 1,
      messageLayout: 'user_health_score_layout',
      timestamp: '2024-01-01T00:00:00Z',
      availableFields: [],
      overallScore: 750,
      changesMade: false,
      goalScores: [
        {
          name: 'Retirement',
          type: 'Retirement',
          score: 85,
          targetAmount: 500000,
          targetAmountInflated: 650000,
          targetDate: new Date('2040-12-31'),
          cashflowTimeseries: [
            [new Date('2024-01-01'), new Date('2040-12-31')],
            [100000, 700000],
          ],
          error: null,
          holdingWarnings: null,
        },
        {
          name: 'House Purchase',
          type: 'Goal',
          score: 60,
          targetAmount: 100000,
          targetAmountInflated: null,
          targetDate: null,
          cashflowTimeseries: [
            [new Date('2024-01-01'), new Date('2030-06-30')],
            [20000, 95000],
          ],
          error: null,
          holdingWarnings: [
            {
              productType: 'ISA',
              providerName: 'Test Provider',
              accountNumber: '12345',
              warning: 'Low performance warning',
            },
          ],
        },
      ],
    };

    wrapper = mount(UserHealthScore, {
      props: {
        message: mockMessage,
      },
      global: {
        stubs: {
          'score-gauge': ScoreGaugeStub,
          'score-donut': ScoreDonutStub,
        },
      },
    });
  });

  describe('Overall Score Display', () => {
    it('renders the overall health score', () => {
      expect(
        wrapper.findElementByText('h1', 'Your Overall Health Score').exists(),
      ).toBe(true);
      const scoreGaugeStub = wrapper.findComponent({ name: 'ScoreGauge' });
      expect(scoreGaugeStub.exists()).toBe(true);
      expect(scoreGaugeStub.props('score')).toBe(750);
      expect(scoreGaugeStub.props('maxScore')).toBe(1000);
    });

    it('handles missing overall score', () => {
      const wrapperNoScore = mount(UserHealthScore, {
        props: {
          message: { ...mockMessage, overallScore: null },
        },
        global: {
          stubs: {
            'score-gauge': ScoreGaugeStub,
            'score-donut': ScoreDonutStub,
          },
        },
      });
      const scoreGaugeStub = wrapperNoScore.findComponent({
        name: 'ScoreGauge',
      });
      expect(scoreGaugeStub.props('score')).toBe(0);
    });
  });

  describe('Goal Scores Display', () => {
    it('renders goal scores when available', () => {
      expect(wrapper.find('[data-testid="goals-section"]').exists()).toBe(true);
      const goalNames = wrapper.findAll('[data-testid="goal-name"]');
      expect(goalNames[0].text()).toBe('Retirement');
      expect(goalNames[1].text()).toBe('House Purchase');
      expect(wrapper.findAll('[data-testid="score-donut-stub"]')).toHaveLength(
        2,
      );
    });

    it('displays goal names and scores', () => {
      const donutStubs = wrapper.findAll('[data-testid="score-donut-stub"]');
      expect(donutStubs[0].text()).toBe('Score: 85');
      expect(donutStubs[1].text()).toBe('Score: 60');
    });

    it('formats target dates correctly', () => {
      const goalDate = wrapper.find('[data-testid="goal-date"]');
      expect(goalDate.text()).toBe('December 2040');
    });

    it('displays goal date when targetDate is present', () => {
      const messageWithTargetDate = {
        ...mockMessage,
        goalScores: [
          {
            name: 'Emergency Fund',
            type: 'Goal',
            score: 75,
            targetAmount: null,
            targetAmountInflated: null,
            targetDate: new Date('2025-06-30'),
            cashflowTimeseries: [[new Date('2024-01-01')], [10000]] as [
              Date[],
              number[],
            ],
            error: null,
            holdingWarnings: null,
          },
        ],
      };

      const wrapperWithTargetDate = mount(UserHealthScore, {
        props: { message: messageWithTargetDate },
        global: {
          stubs: {
            'score-gauge': ScoreGaugeStub,
            'score-donut': ScoreDonutStub,
          },
        },
      });

      const goalDate = wrapperWithTargetDate.find('[data-testid="goal-date"]');
      expect(goalDate.exists()).toBe(true);
      expect(goalDate.text()).toBe('June 2025');
    });

    it('does not display goal date when no targetDate and not Retirement with cashflow', () => {
      const messageWithoutDate = {
        ...mockMessage,
        goalScores: [
          {
            name: 'Simple Goal',
            type: 'Goal',
            score: 50,
            targetAmount: null,
            targetAmountInflated: null,
            targetDate: null,
            cashflowTimeseries: null,
            error: null,
            holdingWarnings: null,
          },
        ],
      };

      const wrapperWithoutDate = mount(UserHealthScore, {
        props: { message: messageWithoutDate },
        global: {
          stubs: {
            'score-gauge': ScoreGaugeStub,
            'score-donut': ScoreDonutStub,
          },
        },
      });

      const goalDate = wrapperWithoutDate.find('[data-testid="goal-date"]');
      expect(goalDate.exists()).toBe(false);
    });

    it('displays goal date for Retirement type with cashflow but no targetDate', () => {
      const messageRetirementNoCashflow = {
        ...mockMessage,
        goalScores: [
          {
            name: 'Retirement Plan',
            type: 'Retirement',
            score: 80,
            targetAmount: null,
            targetAmountInflated: null,
            targetDate: null,
            cashflowTimeseries: [
              [new Date('2024-01-01'), new Date('2045-12-31')],
              [50000, 800000],
            ] as [Date[], number[]],
            error: null,
            holdingWarnings: null,
          },
        ],
      };

      const wrapperRetirement = mount(UserHealthScore, {
        props: { message: messageRetirementNoCashflow },
        global: {
          stubs: {
            'score-gauge': ScoreGaugeStub,
            'score-donut': ScoreDonutStub,
          },
        },
      });

      const goalDate = wrapperRetirement.find('[data-testid="goal-date"]');
      expect(goalDate.exists()).toBe(true);
      expect(goalDate.text()).toBe('December 2045');
    });

    it('does not render goals section when no goals exist', () => {
      const wrapperNoGoals = mount(UserHealthScore, {
        props: {
          message: { ...mockMessage, goalScores: [] },
        },
        global: {
          stubs: {
            'score-gauge': ScoreGaugeStub,
            'score-donut': ScoreDonutStub,
          },
        },
      });
      expect(
        wrapperNoGoals.find('[data-testid="goals-section"]').exists(),
      ).toBe(false);
    });
  });

  describe('Projection Pills', () => {
    it('displays projection amounts', () => {
      const projectionAmounts = wrapper.findAll(
        '[data-testid="projection-amount"]',
      );
      expect(projectionAmounts[0].text()).toBe('£700K');
      expect(projectionAmounts[1].text()).toBe('£95,000');
    });

    it('displays target amounts with inflation', () => {
      const targetBaseAmount = wrapper.find(
        '[data-testid="target-base-amount"]',
      );
      const targetInflationAmount = wrapper.find(
        '[data-testid="target-inflation-amount"]',
      );
      expect(targetBaseAmount.text()).toBe('£500K');
      expect(targetInflationAmount.text()).toBe('+£150K');
    });

    it('applies correct CSS classes for successful projections', () => {
      const projectionAmounts = wrapper.findAll(
        '[data-testid="projection-amount"]',
      );
      // First projection (£700K) vs target (650K inflated) → 700K >= 650K → green
      expect(projectionAmounts[0].classes()).toContain('bg-green-100');
      expect(projectionAmounts[0].classes()).toContain('text-green-700');
      // Second projection (£95K) vs target (100K) → 95K < 100K → red
      expect(projectionAmounts[1].classes()).toContain('bg-red-100');
      expect(projectionAmounts[1].classes()).toContain('text-red-700');
    });
  });

  describe('Holding Warnings', () => {
    it('displays holding warnings when present', () => {
      const warningsTitle = wrapper.find(
        '[data-testid="holding-warnings-title"]',
      );
      const warningItems = wrapper.findAll(
        '[data-testid="holding-warning-item"]',
      );
      expect(warningsTitle.text()).toBe('Warnings:');
      expect(warningItems).toHaveLength(1);
      expect(warningItems[0].text()).toContain('ISA - Test Provider');
      expect(warningItems[0].text()).toContain('Account: 12345');
      expect(warningItems[0].text()).toContain('Low performance warning');
    });

    it('does not display warnings section when no warnings exist', () => {
      const messageWithoutWarnings = {
        ...mockMessage,
        goalScores: [
          {
            ...mockMessage.goalScores![0],
            holdingWarnings: null,
          },
        ],
      };

      const wrapperNoWarnings = mount(UserHealthScore, {
        props: { message: messageWithoutWarnings },
        global: {
          stubs: {
            'score-gauge': ScoreGaugeStub,
            'score-donut': ScoreDonutStub,
          },
        },
      });
      expect(
        wrapperNoWarnings.find('[data-testid="holding-warnings"]').exists(),
      ).toBe(false);
    });

    it('displays warnings even when no cashflow data exists', () => {
      const messageWithWarningsNoCashflow = {
        ...mockMessage,
        goalScores: [
          {
            name: 'Simple Goal',
            type: 'Goal',
            score: 50,
            targetAmount: null,
            targetAmountInflated: null,
            targetDate: null,
            cashflowTimeseries: null,
            error: null,
            holdingWarnings: [
              {
                productType: 'ISA',
                providerName: 'Test Provider',
                accountNumber: '12345',
                warning: 'Test warning without cashflow',
              },
            ],
          },
        ],
      };

      const wrapperWarningsNoCashflow = mount(UserHealthScore, {
        props: { message: messageWithWarningsNoCashflow },
        global: {
          stubs: {
            'score-gauge': ScoreGaugeStub,
            'score-donut': ScoreDonutStub,
          },
        },
      });

      expect(
        wrapperWarningsNoCashflow
          .find('[data-testid="holding-warnings"]')
          .exists(),
      ).toBe(true);
      const warningItem = wrapperWarningsNoCashflow.find(
        '[data-testid="holding-warning-item"]',
      );
      expect(warningItem.text()).toContain('Test warning without cashflow');
    });
  });

  describe('Error Handling', () => {
    it('displays goal errors when present', () => {
      const messageWithError = {
        ...mockMessage,
        goalScores: [
          {
            ...mockMessage.goalScores![0],
            error: 'Calculation error occurred',
          },
        ],
      };

      const wrapperWithError = mount(UserHealthScore, {
        props: { message: messageWithError },
        global: {
          stubs: {
            'score-gauge': ScoreGaugeStub,
            'score-donut': ScoreDonutStub,
          },
        },
      });
      const errorElement = wrapperWithError.find('[data-testid="goal-error"]');
      expect(errorElement.text()).toBe('Calculation error occurred');
    });

    it('displays errors even when no cashflow data exists', () => {
      const messageWithErrorNoCashflow = {
        ...mockMessage,
        goalScores: [
          {
            name: 'Simple Goal',
            type: 'Goal',
            score: 50,
            targetAmount: null,
            targetAmountInflated: null,
            targetDate: null,
            cashflowTimeseries: null,
            error: 'Error without cashflow data',
            holdingWarnings: null,
          },
        ],
      };

      const wrapperErrorNoCashflow = mount(UserHealthScore, {
        props: { message: messageWithErrorNoCashflow },
        global: {
          stubs: {
            'score-gauge': ScoreGaugeStub,
            'score-donut': ScoreDonutStub,
          },
        },
      });

      const errorElement = wrapperErrorNoCashflow.find(
        '[data-testid="goal-error"]',
      );
      expect(errorElement.exists()).toBe(true);
      expect(errorElement.text()).toBe('Error without cashflow data');
    });
  });

  describe('Currency Formatting', () => {
    it('formats large amounts correctly', () => {
      const projectionAmount = wrapper.find(
        '[data-testid="projection-amount"]',
      );
      const targetBaseAmount = wrapper.find(
        '[data-testid="target-base-amount"]',
      );
      expect(projectionAmount.text()).toBe('£700K');
      expect(targetBaseAmount.text()).toBe('£500K');
    });

    it('formats smaller amounts with commas', () => {
      const messageWithSmallAmount = {
        ...mockMessage,
        goalScores: [
          {
            ...mockMessage.goalScores![0],
            cashflowTimeseries: [[new Date('2024-01-01')], [50000]] as [
              Date[],
              number[],
            ],
          },
        ],
      };

      const smallWrapper = mount(UserHealthScore, {
        props: { message: messageWithSmallAmount },
        global: {
          stubs: {
            'score-gauge': ScoreGaugeStub,
            'score-donut': ScoreDonutStub,
          },
        },
      });

      const projectionAmount = smallWrapper.find(
        '[data-testid="projection-amount"]',
      );
      expect(projectionAmount.text()).toBe('£50,000');
    });

    it('formats millions correctly', () => {
      const messageWithMillions = {
        ...mockMessage,
        goalScores: [
          {
            ...mockMessage.goalScores![0],
            cashflowTimeseries: [[new Date('2024-01-01')], [2500000]] as [
              Date[],
              number[],
            ],
          },
        ],
      };

      const millionsWrapper = mount(UserHealthScore, {
        props: { message: messageWithMillions },
        global: {
          stubs: {
            'score-gauge': ScoreGaugeStub,
            'score-donut': ScoreDonutStub,
          },
        },
      });

      const projectionAmount = millionsWrapper.find(
        '[data-testid="projection-amount"]',
      );
      expect(projectionAmount.text()).toBe('£2.5M');
    });
  });

  describe('Chart Canvas Elements', () => {
    it('renders canvas elements for goals with cashflow data', () => {
      const canvasElements = wrapper.findAll('canvas');
      expect(canvasElements).toHaveLength(2);
    });

    it('does not render canvas for goals without cashflow data', () => {
      const messageWithoutCashflow = {
        ...mockMessage,
        goalScores: [
          {
            name: 'Simple Goal',
            type: 'Goal',
            score: 50,
            targetAmount: null,
            targetAmountInflated: null,
            targetDate: null,
            cashflowTimeseries: null,
            error: null,
            holdingWarnings: null,
          },
        ],
      };

      const wrapperNoCashflow = mount(UserHealthScore, {
        props: { message: messageWithoutCashflow },
        global: {
          stubs: {
            'score-gauge': ScoreGaugeStub,
            'score-donut': ScoreDonutStub,
          },
        },
      });
      expect(wrapperNoCashflow.findAll('canvas')).toHaveLength(0);
    });
  });

  describe('Content Section v-if Coverage', () => {
    it('renders content section when goal has cashflowTimeseries', () => {
      // Using existing wrapper which has cashflow data
      const projectionAmounts = wrapper.findAll(
        '[data-testid="projection-amount"]',
      );
      expect(projectionAmounts).toHaveLength(2);
    });

    it('does not render content section when goal has no cashflowTimeseries', () => {
      const messageNoCashflow = {
        ...mockMessage,
        goalScores: [
          {
            name: 'Basic Goal',
            type: 'Goal',
            score: 60,
            targetAmount: 50000,
            targetAmountInflated: null,
            targetDate: null,
            cashflowTimeseries: null,
            error: null,
            holdingWarnings: null,
          },
        ],
      };

      const wrapperNoCashflow = mount(UserHealthScore, {
        props: { message: messageNoCashflow },
        global: {
          stubs: {
            'score-gauge': ScoreGaugeStub,
            'score-donut': ScoreDonutStub,
          },
        },
      });

      expect(
        wrapperNoCashflow.find('[data-testid="projection-amount"]').exists(),
      ).toBe(false);
      expect(
        wrapperNoCashflow.find('[data-testid="target-amount"]').exists(),
      ).toBe(false);
    });
  });
});
