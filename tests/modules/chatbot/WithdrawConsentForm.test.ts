import { beforeEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import { createTestingPinia } from '@pinia/testing';
import { useChatbotStore } from '@modules/chatbot/stores/chatbotStore';
import { useUserStore } from '@modules/users/stores/userStore';
import { WithdrawConsentFormValues } from '@modules/chatbot/types/form-model';
import WithdrawConsentForm from '@modules/chatbot/components/messages/WithdrawConsentForm.vue';

// Mock vue-router
const routerGo = vi.fn();
vi.mock('vue-router', () => ({
  useRouter: () => ({
    go: routerGo,
  }),
}));

// Mock aws-amplify/auth
vi.mock('aws-amplify/auth', () => ({
  fetchAuthSession: vi.fn(),
}));

// Import the mocked function
import { fetchAuthSession } from 'aws-amplify/auth';
const mockFetchAuthSession = vi.mocked(fetchAuthSession);

describe('WithdrawConsentForm.vue', () => {
  let wrapper: ReturnType<typeof mount>;
  let chatbotStore: ReturnType<typeof useChatbotStore>;
  let userStore: ReturnType<typeof useUserStore>;
  let mockMessage: WithdrawConsentFormValues;
  let pinia: ReturnType<typeof createTestingPinia>;

  beforeEach(async () => {
    vi.clearAllMocks();

    pinia = createTestingPinia({
      stubActions: false,
      createSpy: vi.fn,
      initialState: {
        userStore: {
          user: {
            id: 'test-user-123',
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
            type: 'client',
            permissions: [],
          },
        },
      },
    });

    chatbotStore = useChatbotStore();
    userStore = useUserStore();

    userStore.user = {
      id: 123,
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      type: 'client',
      permissions: [],
    };

    chatbotStore.postMessage = vi.fn().mockResolvedValue(undefined);
    chatbotStore.newConversation = vi.fn();
    userStore.getUserData = vi.fn().mockResolvedValue(undefined);

    mockMessage = {
      id: 1,
      messageLayout: 'user__consent_withdraw_layout',
      completed: false,
      withdrawn: false,
      timestamp: '2024-01-01T00:00:00Z',
    };

    wrapper = mount(WithdrawConsentForm, {
      props: {
        message: mockMessage,
      },
      global: {
        plugins: [pinia],
      },
    });

    await flushPromises();
  });

  describe('Initial rendering', () => {
    it('renders the withdraw consent form by default', () => {
      expect(
        wrapper
          .findElementByText('h1', 'Withdraw Data Access Permission')
          .exists(),
      ).toBe(true);
      expect(
        wrapper.find('[data-testid="initial-description"]').text(),
      ).toContain('Withdrawing your consent will prevent Avril from accessing');
      expect(wrapper.find('[data-testid="confirm-button"]').text()).toBe(
        'Confirm Withdrawal',
      );
      expect(wrapper.find('[data-testid="cancel-button"]').text()).toBe(
        'Cancel',
      );
    });

    it('shows confirmation message when message is completed and withdrawn', async () => {
      const completedMessage = {
        ...mockMessage,
        completed: true,
        withdrawn: true,
      };

      wrapper = mount(WithdrawConsentForm, {
        props: {
          message: completedMessage,
        },
        global: {
          plugins: [pinia],
        },
      });

      await nextTick();

      expect(wrapper.find('[data-testid="confirmation-title"]').text()).toBe(
        'Permission Withdrawal Confirmed',
      );
      expect(
        wrapper.find('[data-testid="confirmation-description"]').text(),
      ).toContain('Your data access permission has been withdrawn');
      expect(wrapper.find('[data-testid="exit-button"]').text()).toBe(
        'Exit Chat',
      );
    });

    it('shows cancel message when message is completed but not withdrawn', async () => {
      const cancelledMessage = {
        ...mockMessage,
        completed: true,
        withdrawn: false,
      };

      wrapper = mount(WithdrawConsentForm, {
        props: {
          message: cancelledMessage,
        },
        global: {
          plugins: [pinia],
        },
      });

      await nextTick();

      expect(wrapper.find('[data-testid="cancel-title"]').text()).toBe(
        'No Changes Made',
      );
      expect(
        wrapper.find('[data-testid="cancel-description"]').text(),
      ).toContain('You can still chat with Avril');
    });
  });

  describe('Button interactions', () => {
    it('calls handleConfirm when Confirm Withdrawal button is clicked', async () => {
      mockFetchAuthSession.mockResolvedValue({});

      // Verify userId is available
      expect(userStore.userId).toBe(123);

      const confirmButton = wrapper.find('[data-testid="confirm-button"]');
      await confirmButton.trigger('click');
      await flushPromises();

      expect(chatbotStore.postMessage).toHaveBeenCalledWith({
        ...mockMessage,
        completed: true,
        withdrawn: true,
      });
      expect(mockFetchAuthSession).toHaveBeenCalledWith({ forceRefresh: true });
      expect(userStore.getUserData).toHaveBeenCalled();
    });

    it('calls handleCancel when Cancel button is clicked', async () => {
      const cancelButton = wrapper.find('[data-testid="cancel-button"]');
      await cancelButton.trigger('click');
      await flushPromises();

      expect(chatbotStore.postMessage).toHaveBeenCalledWith({
        ...mockMessage,
        completed: true,
        withdrawn: false,
      });
    });

    it('calls handleExitChat when Exit Chat button is clicked', async () => {
      const confirmedMessage = {
        ...mockMessage,
        completed: true,
        withdrawn: true,
      };

      wrapper = mount(WithdrawConsentForm, {
        props: {
          message: confirmedMessage,
        },
        global: {
          plugins: [pinia],
        },
      });

      await nextTick();

      const exitButton = wrapper.find('[data-testid="exit-button"]');
      await exitButton.trigger('click');

      expect(routerGo).toHaveBeenCalledWith(-1);
      expect(chatbotStore.newConversation).toHaveBeenCalled();
    });
  });

  describe('Loading states', () => {
    it('shows loading state during withdrawal process', async () => {
      mockFetchAuthSession.mockImplementation(
        () => new Promise((resolve) => setTimeout(resolve, 100)),
      );

      const confirmButton = wrapper.find('[data-testid="confirm-button"]');
      await confirmButton.trigger('click');
      await nextTick();

      expect(confirmButton.text()).toBe('Withdrawing...');
      expect(confirmButton.attributes('disabled')).toBeDefined();

      const cancelButton = wrapper.find('[data-testid="cancel-button"]');
      expect(cancelButton.attributes('disabled')).toBeDefined();
    });

    it('resets loading state after withdrawal completes', async () => {
      mockFetchAuthSession.mockResolvedValue({});

      const confirmButton = wrapper.find('[data-testid="confirm-button"]');
      await confirmButton.trigger('click');
      await flushPromises();

      // Should show confirmation view, not loading state
      expect(wrapper.find('[data-testid="confirmation-title"]').text()).toBe(
        'Permission Withdrawal Confirmed',
      );
    });

    it('resets loading state even if withdrawal fails', async () => {
      mockFetchAuthSession.mockRejectedValue(new Error('Auth failed'));

      // Suppress console error for this test
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const confirmButton = wrapper.find('[data-testid="confirm-button"]');
      await confirmButton.trigger('click');
      await flushPromises();

      // Should still be in initial state with buttons enabled
      expect(confirmButton.text()).toBe('Confirm Withdrawal');
      expect(confirmButton.attributes('disabled')).toBeUndefined();

      consoleErrorSpy.mockRestore();
    });
  });

  describe('Disabled prop', () => {
    it('disables buttons when disabled prop is true', async () => {
      await wrapper.setProps({
        message: mockMessage,
        disabled: true,
      });

      const confirmButton = wrapper.find('[data-testid="confirm-button"]');
      const cancelButton = wrapper.find('[data-testid="cancel-button"]');

      expect(confirmButton.attributes('disabled')).toBeDefined();
      expect(cancelButton.attributes('disabled')).toBeDefined();
    });

    it('disables exit button when disabled prop is true in confirmation state', async () => {
      const confirmedMessage = {
        ...mockMessage,
        completed: true,
        withdrawn: true,
      };

      wrapper = mount(WithdrawConsentForm, {
        props: {
          message: confirmedMessage,
          disabled: true,
        },
        global: {
          plugins: [pinia],
        },
      });

      await nextTick();

      const exitButton = wrapper.find('[data-testid="exit-button"]');
      expect(exitButton.attributes('disabled')).toBeDefined();
    });
  });

  describe('Error handling', () => {
    it('does not proceed with withdrawal if userId is not available', async () => {
      // Create a new wrapper with no userId for this test
      const noUserPinia = createTestingPinia({
        stubActions: false,
        createSpy: vi.fn,
        initialState: {
          userStore: {
            user: null,
          },
        },
      });

      const noUserWrapper = mount(WithdrawConsentForm, {
        props: {
          message: mockMessage,
        },
        global: {
          plugins: [noUserPinia],
        },
      });

      const confirmButton = noUserWrapper.find(
        '[data-testid="confirm-button"]',
      );
      await confirmButton.trigger('click');
      await flushPromises();

      expect(chatbotStore.postMessage).not.toHaveBeenCalled();
    });

    it('handles postMessage errors gracefully', async () => {
      chatbotStore.postMessage = vi
        .fn()
        .mockRejectedValue(new Error('API Error'));

      // Suppress console error for this test
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const confirmButton = wrapper.find('[data-testid="confirm-button"]');
      await confirmButton.trigger('click');
      await flushPromises();

      // Should reset loading state even on error
      expect(confirmButton.text()).toBe('Confirm Withdrawal');
      expect(confirmButton.attributes('disabled')).toBeUndefined();

      consoleErrorSpy.mockRestore();
    });

    it('handles getUserData errors gracefully', async () => {
      mockFetchAuthSession.mockResolvedValue({});
      userStore.getUserData = vi
        .fn()
        .mockRejectedValue(new Error('User data error'));

      // Suppress console error for this test
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const confirmButton = wrapper.find('[data-testid="confirm-button"]');
      await confirmButton.trigger('click');
      await flushPromises();

      // Should NOT show confirmation since getUserData failed and prevented showConfirmation.value = true
      expect(
        wrapper
          .findElementByText('h1', 'Withdraw Data Access Permission')
          .exists(),
      ).toBe(true);
      // But the loading state should be reset (isWithdrawing = false in finally block)
      expect(confirmButton.text()).toBe('Confirm Withdrawal');
      expect(confirmButton.attributes('disabled')).toBeUndefined();
      // Should show withdrawal error message
      expect(wrapper.find('[data-testid="withdrawal-error"]').exists()).toBe(
        true,
      );
      expect(wrapper.find('[data-testid="withdrawal-error"]').text()).toBe(
        'Failed to withdraw consent. Please try again.',
      );

      consoleErrorSpy.mockRestore();
    });

    it('shows withdrawal error message when postMessage fails', async () => {
      // Create a fresh wrapper for this test with failing postMessage
      const errorTestPinia = createTestingPinia({
        stubActions: false,
        createSpy: vi.fn,
      });

      const errorChatbotStore = useChatbotStore(errorTestPinia);
      const errorUserStore = useUserStore(errorTestPinia);

      errorUserStore.user = {
        id: 123,
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        type: 'client',
        permissions: [],
      };

      errorChatbotStore.postMessage = vi
        .fn()
        .mockRejectedValue(new Error('API Error'));
      errorUserStore.getUserData = vi.fn().mockResolvedValue(undefined);
      mockFetchAuthSession.mockResolvedValue({});

      const errorWrapper = mount(WithdrawConsentForm, {
        props: {
          message: { ...mockMessage },
        },
        global: {
          plugins: [errorTestPinia],
        },
      });

      await flushPromises();

      // Suppress console error for this test
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const confirmButton = errorWrapper.find('[data-testid="confirm-button"]');
      await confirmButton.trigger('click');
      await flushPromises();

      // Should show withdrawal error message
      expect(
        errorWrapper.find('[data-testid="withdrawal-error"]').exists(),
      ).toBe(true);
      expect(errorWrapper.find('[data-testid="withdrawal-error"]').text()).toBe(
        'Failed to withdraw consent. Please try again.',
      );
      // Should reset loading state
      expect(confirmButton.text()).toBe('Confirm Withdrawal');
      expect(confirmButton.attributes('disabled')).toBeUndefined();

      consoleErrorSpy.mockRestore();
    });

    it('shows cancellation error message when postMessage fails', async () => {
      // Create a fresh wrapper for this test with failing postMessage
      const errorTestPinia = createTestingPinia({
        stubActions: false,
        createSpy: vi.fn,
      });

      const errorChatbotStore = useChatbotStore(errorTestPinia);
      const errorUserStore = useUserStore(errorTestPinia);

      errorUserStore.user = {
        id: 123,
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        type: 'client',
        permissions: [],
      };

      errorChatbotStore.postMessage = vi
        .fn()
        .mockRejectedValue(new Error('API Error'));

      const errorWrapper = mount(WithdrawConsentForm, {
        props: {
          message: { ...mockMessage },
        },
        global: {
          plugins: [errorTestPinia],
        },
      });

      await flushPromises();

      // Suppress console error for this test
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const cancelButton = errorWrapper.find('[data-testid="cancel-button"]');
      await cancelButton.trigger('click');
      await flushPromises();

      // Should show cancellation error message
      expect(
        errorWrapper.find('[data-testid="cancellation-error"]').exists(),
      ).toBe(true);
      expect(
        errorWrapper.find('[data-testid="cancellation-error"]').text(),
      ).toBe('Failed to process cancellation. Please try again.');
      // Should not show cancel message on error
      expect(errorWrapper.find('[data-testid="cancel-message"]').exists()).toBe(
        false,
      );

      consoleErrorSpy.mockRestore();
    });

    it('does not show error messages initially', () => {
      expect(wrapper.find('[data-testid="withdrawal-error"]').exists()).toBe(
        false,
      );
      expect(wrapper.find('[data-testid="cancellation-error"]').exists()).toBe(
        false,
      );
    });
  });

  describe('State transitions', () => {
    it('transitions from initial state to cancel message', async () => {
      expect(
        wrapper
          .findElementByText('h1', 'Withdraw Data Access Permission')
          .exists(),
      ).toBe(true);

      const cancelButton = wrapper.find('[data-testid="cancel-button"]');
      await cancelButton.trigger('click');
      await flushPromises();

      expect(wrapper.find('[data-testid="cancel-title"]').text()).toBe(
        'No Changes Made',
      );
    });

    it('transitions from initial state to confirmation message', async () => {
      mockFetchAuthSession.mockResolvedValue({});

      expect(
        wrapper
          .findElementByText('h1', 'Withdraw Data Access Permission')
          .exists(),
      ).toBe(true);

      const confirmButton = wrapper.find('[data-testid="confirm-button"]');
      await confirmButton.trigger('click');
      await flushPromises();

      expect(wrapper.find('[data-testid="confirmation-title"]').text()).toBe(
        'Permission Withdrawal Confirmed',
      );
    });
  });

  describe('Message mutation', () => {
    it('correctly updates message properties on confirmation', async () => {
      mockFetchAuthSession.mockResolvedValue({});

      const confirmButton = wrapper.find('[data-testid="confirm-button"]');
      await confirmButton.trigger('click');
      await flushPromises();

      expect(mockMessage.completed).toBe(true);
      expect(mockMessage.withdrawn).toBe(true);
    });

    it('correctly updates message properties on cancellation', async () => {
      const cancelButton = wrapper.find('[data-testid="cancel-button"]');
      await cancelButton.trigger('click');
      await flushPromises();

      expect(mockMessage.completed).toBe(true);
      expect(mockMessage.withdrawn).toBe(false);
    });
  });
});
