import getMessages from '@modules/chatbot/api/get-messages';
import { beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('@/services/api', () => ({
  apiClient: {
    get: vi.fn(),
  },
  UnauthorizedError: class UnauthorizedError extends Error {
    constructor(message: string) {
      super(message);
      this.name = 'UnauthorizedError';
    }
  },
}));

vi.mock('@modules/chatbot/utils/mappers/dtoToMessageMapper', () => ({
  dtoToMessageMapper: vi.fn(),
}));

import { UnauthorizedError, apiClient } from '@/services/api';
import { dtoToMessageMapper } from '@modules/chatbot/utils/mappers/dtoToMessageMapper';

describe('getMessages', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('should fetch messages and map them correctly', async () => {
    const dummyDto1 = { id: 1 };
    const dummyDto2 = { id: 2 };
    const mappedMessage1 = { message: 'mapped1' };
    const mappedMessage2 = { message: 'mapped2' };

    (apiClient.get as unknown as ReturnType<typeof vi.fn>).mockResolvedValue([
      dummyDto1,
      dummyDto2,
    ]);

    (
      dtoToMessageMapper as unknown as ReturnType<typeof vi.fn>
    ).mockImplementation(async (dto) => {
      if (dto.id === 1) return mappedMessage1;
      if (dto.id === 2) return mappedMessage2;
    });

    const result = await getMessages();

    expect(apiClient.get).toHaveBeenCalledWith('/api/v2/chatbot/messages');
    expect(dtoToMessageMapper).toHaveBeenCalledTimes(2);
    expect(dtoToMessageMapper).toHaveBeenNthCalledWith(1, dummyDto1);
    expect(dtoToMessageMapper).toHaveBeenNthCalledWith(2, dummyDto2);
    expect(result).toEqual([mappedMessage1, mappedMessage2]);
  });

  it('yields an assistant error message when apiClient.get throws', async () => {
    // Arrange: make apiClient.get reject with any error
    (apiClient.get as any).mockRejectedValue(new Error('Network failure'));

    const results = await getMessages();

    // Assert: exactly one assistant‐style error message is yielded
    expect(results).toHaveLength(1);
    expect(results[0]).toEqual({
      id: expect.any(Number),
      timestamp: expect.any(String), // ISO string
      messageLayout: 'assistant_text_layout',
      message:
        'Unfortunately our server had an issue retrieving your existing conversation. Please try again in a moment.',
    });

    // And ensure we never try to map chunks
    expect(dtoToMessageMapper).not.toHaveBeenCalled();
  });

  it('re-throws UnauthorizedError instead of yielding error message', async () => {
    const unauthorizedError = new UnauthorizedError('Unauthorized', undefined);
    (apiClient.get as any).mockRejectedValue(unauthorizedError);

    await expect(getMessages()).rejects.toThrow(unauthorizedError);
    expect(dtoToMessageMapper).not.toHaveBeenCalled();
  });

  it('re-throws errors with 401 status codes instead of yielding error message', async () => {
    const error401 = {
      $metadata: { httpStatusCode: 401 },
      message: 'Unauthorized',
    };
    (apiClient.get as any).mockRejectedValue(error401);

    await expect(getMessages()).rejects.toBe(error401);
    expect(dtoToMessageMapper).not.toHaveBeenCalled();
  });

  it('re-throws errors with various 401 status formats', async () => {
    const testCases = [
      { $metadata: { httpStatusCode: 401 } },
      { _response: { statusCode: 401 } },
    ];

    for (const errorObj of testCases) {
      vi.resetAllMocks();
      (apiClient.get as any).mockRejectedValue(errorObj);

      await expect(getMessages()).rejects.toBe(errorObj);
      expect(dtoToMessageMapper).not.toHaveBeenCalled();
    }
  });
});
