import newConversation from '@modules/chatbot/api/new-conversation';
import { beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('@/services/api', () => ({
  apiClient: {
    get: vi.fn(),
  },
}));

vi.mock('@modules/chatbot/utils/mappers/dtoToMessageMapper', () => ({
  dtoToMessageMapper: vi.fn(),
}));

import { apiClient } from '@/services/api';
import { dtoToMessageMapper } from '@modules/chatbot/utils/mappers/dtoToMessageMapper';

describe('newConversation', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('should fetch messages and map them correctly', async () => {
    const dummyDto1 = { id: 1 };
    const dummyDto2 = { id: 2 };
    const mappedMessage1 = { message: 'mapped1' };
    const mappedMessage2 = { message: 'mapped2' };

    (apiClient.get as unknown as ReturnType<typeof vi.fn>).mockResolvedValue([
      dummyDto1,
      dummyDto2,
    ]);

    (
      dtoToMessageMapper as unknown as ReturnType<typeof vi.fn>
    ).mockImplementation((dto) => {
      if (dto.id === 1) return mappedMessage1;
      if (dto.id === 2) return mappedMessage2;
    });

    const result = await newConversation();

    expect(apiClient.get).toHaveBeenCalledWith(
      '/api/v2/chatbot/new-conversation',
    );
    expect(dtoToMessageMapper).toHaveBeenCalledTimes(2);
    expect(dtoToMessageMapper).toHaveBeenNthCalledWith(1, dummyDto1);
    expect(dtoToMessageMapper).toHaveBeenNthCalledWith(2, dummyDto2);
    expect(result).toEqual([mappedMessage1, mappedMessage2]);
  });

  it('yields an assistant error message when apiClient.get throws', async () => {
    // Arrange: make apiClient.get reject with any error
    (apiClient.get as any).mockRejectedValue(new Error('Network failure'));

    const results = await newConversation();

    // Assert: exactly one assistant‐style error message is yielded
    expect(results).toHaveLength(1);
    expect(results[0]).toEqual({
      id: expect.any(Number),
      timestamp: expect.any(String), // ISO string
      messageLayout: 'assistant_text_layout',
      message:
        'Unfortunately our server had an issue creating a new conversation. Please try again in a moment.',
    });

    // And ensure we never try to map chunks
    expect(dtoToMessageMapper).not.toHaveBeenCalled();
  });
});
