import { beforeEach, describe, expect, it, vi } from 'vitest';
import postMessage from '@modules/chatbot/api/post-message';

vi.mock('@/services/api', () => ({
  apiClient: {
    post: vi.fn(),
  },
  UnauthorizedError: class UnauthorizedError extends Error {
    constructor(message: string) {
      super(message);
      this.name = 'UnauthorizedError';
    }
  },
}));

vi.mock('@modules/chatbot/utils/mappers/dtoToMessageMapper', () => ({
  dtoToMessageMapper: vi.fn(),
}));

import { UnauthorizedError, apiClient } from '@/services/api';
import { dtoToMessageMapper } from '@modules/chatbot/utils/mappers/dtoToMessageMapper';

const textEncoder = new TextEncoder();

describe('postMessage', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('yields a mapped message from a single chunk with delimiter', async () => {
    const message = { message: 'hello' };
    const mappedMessage = { message: 'mapped hello' };

    (dtoToMessageMapper as any).mockResolvedValue(mappedMessage);

    const jsonStr = JSON.stringify(message);
    const chunk = jsonStr + '\n---\n';

    const stream = new ReadableStream<Uint8Array>({
      start(controller) {
        controller.enqueue(textEncoder.encode(chunk));
        controller.close();
      },
    });

    (apiClient.post as any).mockResolvedValue(stream);

    const results: any[] = [];
    for await (const val of postMessage({
      message: 'hello',
      timestamp: '',
      data_layout: 'user_text_layout',
    })) {
      results.push(val);
    }

    expect(results).toEqual([mappedMessage]);
  });

  it('handles a message split across multiple chunks', async () => {
    const message = { text: 'split' };
    const mappedMessage = { text: 'mapped split' };

    (dtoToMessageMapper as any).mockResolvedValue(mappedMessage);

    const jsonStr = JSON.stringify(message);
    const mid = Math.floor(jsonStr.length / 2);
    const part1 = jsonStr.slice(0, mid);
    const part2 = jsonStr.slice(mid) + '\n---\n';

    const stream = new ReadableStream<Uint8Array>({
      async start(controller) {
        controller.enqueue(textEncoder.encode(part1));
        await new Promise((resolve) => setTimeout(resolve, 10));
        controller.enqueue(textEncoder.encode(part2));
        controller.close();
      },
    });

    (apiClient.post as any).mockResolvedValue(stream);

    const results: any[] = [];
    for await (const val of postMessage({
      message: 'split',
      timestamp: '',
      data_layout: 'user_text_layout',
    })) {
      results.push(val);
    }
    expect(results).toEqual([mappedMessage]);
  });

  it('processes remaining data without a trailing delimiter', async () => {
    const message = { text: 'remaining' };
    const mappedMessage = { text: 'mapped remaining' };

    (dtoToMessageMapper as any).mockResolvedValue(mappedMessage);

    const jsonStr = JSON.stringify(message);
    const stream = new ReadableStream<Uint8Array>({
      start(controller) {
        controller.enqueue(textEncoder.encode(jsonStr));
        controller.close();
      },
    });

    (apiClient.post as any).mockResolvedValue(stream);

    const results: any[] = [];
    for await (const val of postMessage({
      message: 'remaining',
      timestamp: '',
      data_layout: 'user_text_layout',
    })) {
      results.push(val);
    }
    expect(results).toEqual([mappedMessage]);
  });

  it('yields multiple messages separated by the delimiter', async () => {
    const message1 = { text: 'first' };
    const message2 = { text: 'second' };
    const mappedMessage1 = { text: 'mapped first' };
    const mappedMessage2 = { text: 'mapped second' };

    (dtoToMessageMapper as any)
      .mockResolvedValueOnce(mappedMessage1)
      .mockResolvedValueOnce(mappedMessage2);

    const jsonStr1 = JSON.stringify(message1);
    const jsonStr2 = JSON.stringify(message2);
    const chunk = jsonStr1 + '\n---\n' + jsonStr2 + '\n---\n';

    const stream = new ReadableStream<Uint8Array>({
      start(controller) {
        controller.enqueue(textEncoder.encode(chunk));
        controller.close();
      },
    });

    (apiClient.post as any).mockResolvedValue(stream);

    const results: any[] = [];
    for await (const val of postMessage({
      message: 'remaining',
      timestamp: '',
      data_layout: 'user_text_layout',
    })) {
      results.push(val);
    }
    expect(results).toEqual([mappedMessage1, mappedMessage2]);
  });

  it('yields an assistant error message when apiClient.post throws prior to the stream starting', async () => {
    // Arrange: make apiClient.post reject with any error
    (apiClient.post as any).mockRejectedValue(new Error('Network failure'));

    const results: any[] = [];
    for await (const val of postMessage({
      message: 'trigger error',
      timestamp: '',
      data_layout: 'user_text_layout',
    })) {
      results.push(val);
    }

    // Assert: exactly one assistant‐style error message is yielded
    expect(results).toHaveLength(1);
    expect(results[0]).toEqual({
      id: expect.any(Number),
      timestamp: expect.any(String), // ISO string
      messageLayout: 'assistant_text_layout',
      message:
        'Unfortunately our server had an issue processing your message. Please try again in a moment.',
    });

    // And ensure we never try to map chunks
    expect(dtoToMessageMapper).not.toHaveBeenCalled();
  });

  it('yields an assistant error message when apiClient.post throws mid stream', async () => {
    // Arrange: simulate a stream whose read() rejects
    const fakeStream = {
      getReader: () => ({
        read: () => Promise.reject(new Error('midstream failure')),
      }),
    } as unknown as ReadableStream<Uint8Array>;

    (apiClient.post as any).mockResolvedValue(fakeStream);

    const results: any[] = [];
    for await (const val of postMessage({
      message: 'trigger midstream error',
      timestamp: '',
      data_layout: 'user_text_layout',
    })) {
      results.push(val);
    }

    // Assert: exactly one assistant‐style error message is yielded
    expect(results).toHaveLength(1);
    expect(results[0]).toEqual({
      id: expect.any(Number),
      timestamp: expect.any(String),
      messageLayout: 'assistant_text_layout',
      message:
        'Unfortunately our server ran into a problem whilst generating the response to your message. Please try again in a moment.',
    });

    // And ensure we never try to map chunks
    expect(dtoToMessageMapper).not.toHaveBeenCalled();
  });

  it('re-throws UnauthorizedError when apiClient.post throws before stream starts', async () => {
    const unauthorizedError = new UnauthorizedError('Unauthorized', undefined);
    (apiClient.post as any).mockRejectedValue(unauthorizedError);

    const asyncGenerator = postMessage({
      message: 'trigger 401',
      timestamp: '',
      data_layout: 'user_text_layout',
    });

    await expect(asyncGenerator.next()).rejects.toThrow(unauthorizedError);
    expect(dtoToMessageMapper).not.toHaveBeenCalled();
  });

  it('re-throws errors with 401 status codes when apiClient.post throws before stream starts', async () => {
    const error401 = {
      $metadata: { httpStatusCode: 401 },
      message: 'Unauthorized',
    };
    (apiClient.post as any).mockRejectedValue(error401);

    const asyncGenerator = postMessage({
      message: 'trigger 401',
      timestamp: '',
      data_layout: 'user_text_layout',
    });

    await expect(asyncGenerator.next()).rejects.toBe(error401);
    expect(dtoToMessageMapper).not.toHaveBeenCalled();
  });

  it('re-throws errors with various 401 status formats', async () => {
    const testCases = [
      { $metadata: { httpStatusCode: 401 } },
      { _response: { statusCode: 401 } },
    ];

    for (const errorObj of testCases) {
      vi.resetAllMocks();
      (apiClient.post as any).mockRejectedValue(errorObj);

      const asyncGenerator = postMessage({
        message: 'trigger 401',
        timestamp: '',
        data_layout: 'user_text_layout',
      });

      await expect(asyncGenerator.next()).rejects.toBe(errorObj);
      expect(dtoToMessageMapper).not.toHaveBeenCalled();
    }
  });
});
