import * as path from 'path';
import { defineConfig } from 'vite';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-expect-error
import vue from '@vitejs/plugin-vue';

const plugins = [vue()];

// https://vitejs.dev/config/
export default defineConfig({
  plugins,
  resolve: {
    alias: {
      '~': path.resolve(__dirname, 'node_modules'),
      '@': path.resolve(__dirname, 'src'),
      '@modules': path.resolve(__dirname, 'src', 'modules'),
      '@tests': path.resolve(__dirname, 'tests'),
      './runtimeConfig': './runtimeConfig.browser',
    },
  },
  build: {
    cssCodeSplit: false,
    lib: {
      entry: ['/src/index.ts'],
      formats: ['es'],
      name: 'aventur-core',
      fileName: (format: string) =>
        format === 'es' ? 'index.js' : `index.${format}`,
      sourcemap: true,
      rollupOptions: {
        external: ['aws-amplify','vue', '@vueuse/core', 'lodash'],
        output: {
          globals: { vue: 'Vue' },
        },
      },
    },
  },
  test: {
    environment: 'jsdom',
    include: ['tests/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    setupFiles: './vitest.setup.ts',
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    coverage: {
      reporter: ['json'],
    },
  },
});
